2025-06-28 22:58:47 - Main - INFO - Starting Server
2025-06-28 22:58:47 - Main - INFO - Connection at: **************:9092
2025-06-28 22:58:47 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 22:58:47 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-28 22:58:47 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 22:58:47 - Ka<PERSON><PERSON>WorkflowConsumer - INFO - Initializing database connections...
2025-06-28 22:58:47 - <PERSON><PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 22:58:49 - <PERSON><PERSON>Manager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 22:58:49 - <PERSON><PERSON>Manager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 22:58:51 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - Successfully connected to Redis on DB index: 6!
