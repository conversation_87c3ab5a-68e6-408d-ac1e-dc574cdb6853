2025-06-28 23:03:27 - Main - INFO - Starting Server
2025-06-28 23:03:27 - Main - INFO - Connection at: **************:9092
2025-06-28 23:03:27 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 23:03:27 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-28 23:03:27 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 23:03:27 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 23:03:27 - <PERSON><PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 23:03:29 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 23:03:29 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
