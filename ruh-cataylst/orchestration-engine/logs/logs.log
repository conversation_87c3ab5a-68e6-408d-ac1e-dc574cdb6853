2025-06-29 00:55:20 - Main - INFO - Starting Server
2025-06-29 00:55:20 - Main - INFO - Connection at: **************:9092
2025-06-29 00:55:20 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-29 00:55:20 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-29 00:55:20 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-29 00:55:20 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-29 00:55:20 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-29 00:55:22 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-29 00:55:22 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-29 00:55:23 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-29 00:55:25 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-29 00:55:25 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-29 00:55:28 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-29 00:55:28 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-29 00:55:28 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-29 00:55:30 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-29 00:55:30 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-29 00:55:32 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-29 00:55:32 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-29 00:55:32 - RedisEventListener - INFO - Redis event listener started
2025-06-29 00:55:32 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-29 00:55:32 - StateManager - DEBUG - Using provided database connections
2025-06-29 00:55:32 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-29 00:55:32 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-29 00:55:32 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-29 00:55:32 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-29 00:55:32 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-29 00:55:32 - StateManager - INFO - WorkflowStateManager initialized
2025-06-29 00:55:32 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-29 00:55:32 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-29 00:55:33 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-29 00:55:33 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-29 00:55:35 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-29 00:55:35 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-29 00:55:35 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-29 00:55:46 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-29 00:55:52 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-29 00:55:52 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-29 00:55:52 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-29 00:55:59 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-29 00:55:59 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-29 00:55:59 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-29 00:56:05 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-29 00:56:05 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-29 00:56:05 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1038
2025-06-29 00:56:05 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': **********, 'task_type': 'workflow', 'data': {'workflow_id': 'feda07bf-a91e-4004-80cb-72416cdb5a43', 'payload': {'user_dependent_fields': ['iteration_list'], 'user_payload_template': {'iteration_list': {'value': '["blockchain","automation","healthcare"]', 'transition_id': 'LoopNode-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-06-29 00:56:05 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-29 00:56:05 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-29 00:56:06 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-29 00:56:06 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow LOOP TEST DO NOT DELETE retrieved successfully",
  "workflow": {
    "id": "feda07bf-a91e-4004-80cb-72416cdb5a43",
    "name": "LOOP TEST DO NOT DELETE",
    "description": "LOOP_TEST_DO_NOT_DELETE",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/16a976d6-6669-4cb4-8207-4f9b8626a0b8.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/91cb74a1-1bfd-42fa-b59d-58773b35d33a.json",
    "start_nodes": [
      {
        "field": "iteration_list",
        "type": "list",
        "transition_id": "transition-LoopNode-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-24T11:13:46.129953",
    "updated_at": "2025-06-28T14:13:59.013729",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "LoopNode",
        "display_name": "For Each Loop",
        "type": "component",
        "transition_id": "transition-LoopNode-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1751052569332"
      },
      {
        "name": "MCP_script-generation-mcp_script_generate",
        "id": "748a8221-d7d9-4352-93ae-00700f4d28b1",
        "transition_id": "transition-MCP_script-generation-mcp_script_generate-*************",
        "type": "mcp",
        "display_name": "script-generation-mcp - script_generate",
        "data": {
          "input_schema": {
            "$defs": {
              "Keywords": {
                "properties": {
                  "time": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Time"
                  },
                  "objective": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Objective"
                  },
                  "audience": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Audience"
                  },
                  "gender": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Gender"
                  },
                  "tone": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Tone"
                  },
                  "speakers": {
                    "anyOf": [
                      {
                        "items": {
                          "type": "string"
                        },
                        "type": "array"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Speakers"
                  }
                },
                "title": "Keywords",
                "type": "object"
              },
              "ScriptType": {
                "enum": [
                  "VIDEO",
                  "TOPIC",
                  "SCRIPT",
                  "BLOG",
                  "AI"
                ],
                "title": "ScriptType",
                "type": "string"
              },
              "VideoType": {
                "enum": [
                  "SHORT",
                  "LONG"
                ],
                "title": "VideoType",
                "type": "string"
              }
            },
            "properties": {
              "topic": {
                "title": "Topic",
                "type": "string"
              },
              "script_type": {
                "$ref": "#/$defs/ScriptType",
                "default": "TOPIC"
              },
              "keywords": {
                "$ref": "#/$defs/Keywords"
              },
              "video_type": {
                "$ref": "#/$defs/VideoType",
                "default": "SHORT"
              },
              "link": {
                "anyOf": [
                  {
                    "format": "uri",
                    "maxLength": 2083,
                    "minLength": 1,
                    "type": "string"
                  },
                  {
                    "type": "null"
                  }
                ],
                "default": null,
                "title": "Link"
              }
            },
            "required": [
              "topic"
            ],
            "title": "GenerateScriptInput",
            "type": "object"
          },
          "output_schema": {
            "properties": {
              "title": {
                "type": "string",
                "description": "Title of the generated script",
                "title": "title"
              },
              "script": {
                "type": "string",
                "description": "The generated script",
                "title": "script"
              },
              "script_type": {
                "type": "string",
                "description": "Type of the script",
                "title": "script_type"
              },
              "video_type": {
                "type": "string",
                "description": "The type of video",
                "title": "video_type"
              },
              "link": {
                "type": "string",
                "format": "uri",
                "description": "Optional link for the script",
                "title": "link"
              }
            }
          }
        }
      }
    ],
    "is_updated": true
  }
}
2025-06-29 00:56:06 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for feda07bf-a91e-4004-80cb-72416cdb5a43 - server_script_path is optional
2025-06-29 00:56:06 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-29 00:56:06 - StateManager - DEBUG - Using global database connections from initializer
2025-06-29 00:56:06 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-29 00:56:06 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-29 00:56:06 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-29 00:56:07 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-29 00:56:07 - StateManager - INFO - WorkflowStateManager initialized
2025-06-29 00:56:07 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-29 00:56:07 - StateManager - DEBUG - Using provided database connections
2025-06-29 00:56:07 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-29 00:56:07 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-29 00:56:07 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-29 00:56:08 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-29 00:56:08 - StateManager - INFO - WorkflowStateManager initialized
2025-06-29 00:56:08 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-29 00:56:08 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751052569332: ['transition-LoopNode-*************']
2025-06-29 00:56:08 - StateManager - INFO - Built dependency map for 3 transitions
2025-06-29 00:56:08 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-29 00:56:08 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751052569332 depends on: ['transition-LoopNode-*************']
2025-06-29 00:56:08 - MCPToolExecutor - DEBUG - Set correlation ID to: fb09d2c5-7239-440c-bd6d-1758a17bce88
2025-06-29 00:56:08 - EnhancedWorkflowEngine - DEBUG - Set correlation_id fb09d2c5-7239-440c-bd6d-1758a17bce88 in tool_executor
2025-06-29 00:56:08 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-29 00:56:08 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-06-29 00:56:08 - NodeExecutor - DEBUG - Set correlation ID to: fb09d2c5-7239-440c-bd6d-1758a17bce88
2025-06-29 00:56:08 - EnhancedWorkflowEngine - DEBUG - Set correlation_id fb09d2c5-7239-440c-bd6d-1758a17bce88 in node_executor
2025-06-29 00:56:08 - AgentExecutor - DEBUG - Set correlation ID to: fb09d2c5-7239-440c-bd6d-1758a17bce88
2025-06-29 00:56:08 - EnhancedWorkflowEngine - DEBUG - Set correlation_id fb09d2c5-7239-440c-bd6d-1758a17bce88 in agent_executor
2025-06-29 00:56:08 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-29 00:56:08 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-06-29 00:56:08 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-29 00:56:08 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: fb09d2c5-7239-440c-bd6d-1758a17bce88
2025-06-29 00:56:08 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: fb09d2c5-7239-440c-bd6d-1758a17bce88
2025-06-29 00:56:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-29 00:56:08 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-29 00:56:08 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-29 00:56:08 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-29 00:56:08 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-29 00:56:08 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:fb09d2c5-7239-440c-bd6d-1758a17bce88'
2025-06-29 00:56:09 - RedisManager - DEBUG - Set key 'workflow_state:fb09d2c5-7239-440c-bd6d-1758a17bce88' with TTL of 600 seconds
2025-06-29 00:56:09 - StateManager - INFO - Workflow state saved to Redis for workflow ID: fb09d2c5-7239-440c-bd6d-1758a17bce88. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 00:56:09 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-29 00:56:09 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-29 00:56:09 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-29 00:56:09 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-29 00:56:09 - StateManager - INFO - Terminated: False
2025-06-29 00:56:09 - StateManager - INFO - Pending transitions (0): []
2025-06-29 00:56:09 - StateManager - INFO - Waiting transitions (0): []
2025-06-29 00:56:09 - StateManager - INFO - Completed transitions (0): []
2025-06-29 00:56:09 - StateManager - INFO - Results stored for 0 transitions
2025-06-29 00:56:09 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-29 00:56:09 - StateManager - INFO - Workflow status: inactive
2025-06-29 00:56:09 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-29 00:56:09 - StateManager - INFO - Workflow status: inactive
2025-06-29 00:56:09 - StateManager - INFO - Workflow paused: False
2025-06-29 00:56:09 - StateManager - INFO - ==============================
2025-06-29 00:56:09 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-29 00:56:09 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:09 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-29 00:56:09 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-29 00:56:09 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-29 00:56:09 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-29 00:56:09 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-29 00:56:09 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-29 00:56:09 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-29 00:56:09 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-29 00:56:09 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-29 00:56:09 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-29 00:56:09 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-29 00:56:09 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:09 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-29 00:56:09 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-29 00:56:09 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-29 00:56:09 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-29 00:56:09 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-29 00:56:09 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-29 00:56:09 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-29 00:56:09 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-29 00:56:09 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-29 00:56:09 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-29 00:56:09 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-29 00:56:09 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-29 00:56:09 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-29 00:56:09 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-29 00:56:09 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-29 00:56:09 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-29 00:56:09 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-29 00:56:09 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-29 00:56:09 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-29 00:56:09 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-29 00:56:09 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-29 00:56:09 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:09 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-29 00:56:09 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain directly
2025-06-29 00:56:09 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-29 00:56:09 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-29 00:56:10 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-29 00:56:10 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 00:56:10 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-29 00:56:10 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-29 00:56:10 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-29 00:56:10 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-29 00:56:11 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-29 00:56:11 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 00:56:11 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-29 00:56:11 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0'}
2025-06-29 00:56:11 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-29 00:56:11 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 273457.214394, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 00:56:11 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-29 00:56:11 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-CombineTextComponent-*************
2025-06-29 00:56:11 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-CombineTextComponent-*************
2025-06-29 00:56:11 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-29 00:56:11 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:11 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 2, 'workflow_status': 'running'}
2025-06-29 00:56:11 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-29 00:56:11 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-29 00:56:11 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-29 00:56:11 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-29 00:56:11 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-29 00:56:12 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-06-29 00:56:14 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:6bec2250-e3ab-4a26-8734-51bbff662137', 'data': b'expired'}
2025-06-29 00:56:14 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:6bec2250-e3ab-4a26-8734-51bbff662137'
2025-06-29 00:56:14 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:6bec2250-e3ab-4a26-8734-51bbff662137
2025-06-29 00:56:14 - RedisEventListener - DEBUG - Extracted key: workflow_state:6bec2250-e3ab-4a26-8734-51bbff662137
2025-06-29 00:56:14 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-29 00:56:14 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-29 00:56:14 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: 6bec2250-e3ab-4a26-8734-51bbff662137
2025-06-29 00:56:14 - RedisEventListener - INFO - Archiving workflow state for workflow: 6bec2250-e3ab-4a26-8734-51bbff662137
2025-06-29 00:56:14 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation fb09d2c5-7239-440c-bd6d-1758a17bce88
2025-06-29 00:56:16 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-06-29 00:56:16 - StateManager - INFO - Using in-memory result for transition transition-LoopNode-*************.
2025-06-29 00:56:16 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-29 00:56:16 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 273457.214394, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-29 00:56:16 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-29 00:56:16 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 273457.214394, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-29 00:56:16 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-29 00:56:16 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-29 00:56:16 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-29 00:56:16 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-29 00:56:16 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-29 00:56:16 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-29 00:56:16 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 00:56:16 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 00:56:16 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 00:56:16 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:16 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 3, 'workflow_status': 'running'}
2025-06-29 00:56:16 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 1cab0bf8-7a0f-44f9-9394-2f337c2a4e72) using provided producer.
2025-06-29 00:56:16 - NodeExecutor - DEBUG - Added correlation_id fb09d2c5-7239-440c-bd6d-1758a17bce88 to payload
2025-06-29 00:56:16 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-29 00:56:16 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '1cab0bf8-7a0f-44f9-9394-2f337c2a4e72', 'correlation_id': 'fb09d2c5-7239-440c-bd6d-1758a17bce88', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-29 00:56:16 - NodeExecutor - DEBUG - Request 1cab0bf8-7a0f-44f9-9394-2f337c2a4e72 sent successfully using provided producer.
2025-06-29 00:56:16 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 1cab0bf8-7a0f-44f9-9394-2f337c2a4e72...
2025-06-29 00:56:16 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1038, corr_id: fb09d2c5-7239-440c-bd6d-1758a17bce88
2025-06-29 00:56:19 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-29 00:56:20 - PostgresManager - DEBUG - Inserted new workflow state for correlation_id: fb09d2c5-7239-440c-bd6d-1758a17bce88
2025-06-29 00:56:20 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: fb09d2c5-7239-440c-bd6d-1758a17bce88
2025-06-29 00:56:21 - NodeExecutor - DEBUG - Result consumer received message: Offset=915
2025-06-29 00:56:21 - NodeExecutor - DEBUG - Received valid result for request_id 1cab0bf8-7a0f-44f9-9394-2f337c2a4e72
2025-06-29 00:56:21 - NodeExecutor - INFO - Result received for request 1cab0bf8-7a0f-44f9-9394-2f337c2a4e72.
2025-06-29 00:56:21 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-29 00:56:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 4, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 00:56:21 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751138781.152278}}
2025-06-29 00:56:21 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-29 00:56:21 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-29 00:56:21 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 00:56:21 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 00:56:21 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'transition-CombineTextComponent-*************'}
2025-06-29 00:56:21 - LoopExecutor - INFO - 🚨 notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-29 00:56:21 - TransitionHandler - ERROR - ❌ Error notifying loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************: object NoneType can't be used in 'await' expression
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:21 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 10.72 seconds
2025-06-29 00:56:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'result': 'Completed transition in 10.72 seconds', 'message': 'Transition completed in 10.72 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 5, 'workflow_status': 'running'}
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:21 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-29 00:56:21 - LoopExecutor - DEBUG - 🔧 Transition transition-CombineTextComponent-************* execution completed, result type: <class 'list'>
2025-06-29 00:56:21 - LoopExecutor - DEBUG - 🔧 Processing result for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:21 - LoopExecutor - DEBUG - 🔗 Transition transition-CombineTextComponent-************* returned next transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:21 - LoopExecutor - DEBUG - 🔧 Extended next_transitions, now: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:21 - LoopExecutor - DEBUG - 🔗 Moving to next transitions in chain: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:21 - LoopExecutor - DEBUG - 🏁 Found exit transitions to execute: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:21 - LoopExecutor - DEBUG - 🚀 Executing exit transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 00:56:21 - LoopExecutor - ERROR - ❌ Exit transition transition-MCP_script-generation-mcp_script_generate-************* not found in transitions_by_id
2025-06-29 00:56:21 - LoopExecutor - DEBUG - 🔍 Available transition IDs: ['transition-LoopNode-*************', 'transition-CombineTextComponent-*************', 'transition-MergeDataComponent-1751052569332']
2025-06-29 00:56:21 - LoopExecutor - ERROR - ❌ Exit transition transition-MCP_script-generation-mcp_script_generate-************* not found in orchestration engine either
2025-06-29 00:56:21 - LoopExecutor - DEBUG - 🔚 All exit transitions executed, chain complete
2025-06-29 00:56:21 - LoopExecutor - DEBUG - 🔚 Chain execution completed, final_result: None
2025-06-29 00:56:21 - LoopExecutor - INFO - ✅ Iteration 1 completed successfully
2025-06-29 00:56:21 - LoopExecutor - INFO - 🔄 Starting iteration 2/3
2025-06-29 00:56:21 - LoopExecutor - DEBUG - 🔄 Executing iteration 2 chain directly
2025-06-29 00:56:21 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in memory: automation
2025-06-29 00:56:22 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_1
2025-06-29 00:56:22 - RedisManager - DEBUG - Set key 'result:loop_iteration_1' with TTL of 900 seconds
2025-06-29 00:56:22 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 00:56:22 - StateManager - INFO - Marked transition loop_iteration_1 as completed (was_pending=False, was_waiting=False)
2025-06-29 00:56:22 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_1', 'current_iteration', 'loop_iteration_0', 'transition-CombineTextComponent-*************'}
2025-06-29 00:56:22 - StateManager - DEBUG - Stored result for transition current_iteration in memory: automation
2025-06-29 00:56:22 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-29 00:56:23 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-29 00:56:23 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 00:56:23 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-29 00:56:23 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_1', 'current_iteration', 'loop_iteration_0', 'transition-CombineTextComponent-*************'}
2025-06-29 00:56:23 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-29 00:56:23 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 'automation', 'iteration_index': 1, 'iteration_metadata': {'timestamp': 273469.552688583, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 00:56:23 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_1
2025-06-29 00:56:23 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-CombineTextComponent-*************
2025-06-29 00:56:23 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-CombineTextComponent-*************
2025-06-29 00:56:23 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-29 00:56:23 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:23 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-06-29 00:56:23 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-29 00:56:23 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-29 00:56:23 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-29 00:56:23 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-29 00:56:23 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-29 00:56:24 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-06-29 00:56:26 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation fb09d2c5-7239-440c-bd6d-1758a17bce88
2025-06-29 00:56:26 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-06-29 00:56:26 - StateManager - INFO - Using in-memory result for transition transition-LoopNode-*************.
2025-06-29 00:56:26 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-29 00:56:26 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'automation', 'iteration_index': 1, 'iteration_metadata': {'timestamp': 273469.552688583, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-29 00:56:26 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-29 00:56:26 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'automation', 'iteration_index': 1, 'iteration_metadata': {'timestamp': 273469.552688583, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': automation
2025-06-29 00:56:26 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-29 00:56:26 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-29 00:56:26 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-29 00:56:26 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-29 00:56:26 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-29 00:56:26 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-29 00:56:26 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'automation', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 00:56:26 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'automation', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 00:56:26 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'automation', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 00:56:26 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:26 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 7, 'workflow_status': 'running'}
2025-06-29 00:56:26 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 1b700e12-5bd0-4ef3-94ba-9c66eb83625d) using provided producer.
2025-06-29 00:56:26 - NodeExecutor - DEBUG - Added correlation_id fb09d2c5-7239-440c-bd6d-1758a17bce88 to payload
2025-06-29 00:56:26 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-29 00:56:26 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'automation', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '1b700e12-5bd0-4ef3-94ba-9c66eb83625d', 'correlation_id': 'fb09d2c5-7239-440c-bd6d-1758a17bce88', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-29 00:56:26 - NodeExecutor - DEBUG - Request 1b700e12-5bd0-4ef3-94ba-9c66eb83625d sent successfully using provided producer.
2025-06-29 00:56:26 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 1b700e12-5bd0-4ef3-94ba-9c66eb83625d...
2025-06-29 00:56:27 - NodeExecutor - DEBUG - Result consumer received message: Offset=916
2025-06-29 00:56:27 - NodeExecutor - DEBUG - Received valid result for request_id 1b700e12-5bd0-4ef3-94ba-9c66eb83625d
2025-06-29 00:56:27 - NodeExecutor - INFO - Result received for request 1b700e12-5bd0-4ef3-94ba-9c66eb83625d.
2025-06-29 00:56:27 - TransitionHandler - INFO - Execution result from Components executor: "automation and AI"
2025-06-29 00:56:27 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:27 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'automation and AI', 'status': 'completed', 'sequence': 8, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 00:56:27 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'automation and AI'}, 'status': 'completed', 'timestamp': 1751138787.538275}}
2025-06-29 00:56:28 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-29 00:56:28 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-29 00:56:28 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 00:56:28 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 00:56:28 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_1', 'current_iteration', 'loop_iteration_0', 'transition-CombineTextComponent-*************'}
2025-06-29 00:56:28 - LoopExecutor - INFO - 🚨 notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-29 00:56:28 - TransitionHandler - ERROR - ❌ Error notifying loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************: object NoneType can't be used in 'await' expression
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:28 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 4.76 seconds
2025-06-29 00:56:28 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:28 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'result': 'Completed transition in 4.76 seconds', 'message': 'Transition completed in 4.76 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 9, 'workflow_status': 'running'}
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:28 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-29 00:56:28 - LoopExecutor - DEBUG - 🔧 Transition transition-CombineTextComponent-************* execution completed, result type: <class 'list'>
2025-06-29 00:56:28 - LoopExecutor - DEBUG - 🔧 Processing result for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:28 - LoopExecutor - DEBUG - 🔗 Transition transition-CombineTextComponent-************* returned next transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:28 - LoopExecutor - DEBUG - 🔧 Extended next_transitions, now: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:28 - LoopExecutor - DEBUG - 🔗 Moving to next transitions in chain: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:28 - LoopExecutor - DEBUG - 🏁 Found exit transitions to execute: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:28 - LoopExecutor - DEBUG - 🚀 Executing exit transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 00:56:28 - LoopExecutor - ERROR - ❌ Exit transition transition-MCP_script-generation-mcp_script_generate-************* not found in transitions_by_id
2025-06-29 00:56:28 - LoopExecutor - DEBUG - 🔍 Available transition IDs: ['transition-LoopNode-*************', 'transition-CombineTextComponent-*************', 'transition-MergeDataComponent-1751052569332']
2025-06-29 00:56:28 - LoopExecutor - ERROR - ❌ Exit transition transition-MCP_script-generation-mcp_script_generate-************* not found in orchestration engine either
2025-06-29 00:56:28 - LoopExecutor - DEBUG - 🔚 All exit transitions executed, chain complete
2025-06-29 00:56:28 - LoopExecutor - DEBUG - 🔚 Chain execution completed, final_result: None
2025-06-29 00:56:28 - LoopExecutor - INFO - ✅ Iteration 2 completed successfully
2025-06-29 00:56:28 - LoopExecutor - INFO - 🔄 Starting iteration 3/3
2025-06-29 00:56:28 - LoopExecutor - DEBUG - 🔄 Executing iteration 3 chain directly
2025-06-29 00:56:28 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in memory: healthcare
2025-06-29 00:56:28 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_2
2025-06-29 00:56:28 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 00:56:29 - RedisManager - DEBUG - Set key 'result:loop_iteration_2' with TTL of 900 seconds
2025-06-29 00:56:29 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 00:56:29 - StateManager - INFO - Marked transition loop_iteration_2 as completed (was_pending=False, was_waiting=False)
2025-06-29 00:56:29 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_2', 'loop_iteration_1', 'transition-CombineTextComponent-*************'}
2025-06-29 00:56:29 - StateManager - DEBUG - Stored result for transition current_iteration in memory: healthcare
2025-06-29 00:56:29 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 00:56:29 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-29 00:56:29 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 00:56:29 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 00:56:29 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-29 00:56:29 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 00:56:29 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-29 00:56:29 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_2', 'loop_iteration_1', 'transition-CombineTextComponent-*************'}
2025-06-29 00:56:29 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-29 00:56:29 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 'healthcare', 'iteration_index': 2, 'iteration_metadata': {'timestamp': 273475.852644083, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 00:56:29 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_2
2025-06-29 00:56:29 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-CombineTextComponent-*************
2025-06-29 00:56:29 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-CombineTextComponent-*************
2025-06-29 00:56:29 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-29 00:56:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 10, 'workflow_status': 'running'}
2025-06-29 00:56:29 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-29 00:56:29 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-29 00:56:29 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-29 00:56:29 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-29 00:56:29 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-29 00:56:30 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-06-29 00:56:32 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation fb09d2c5-7239-440c-bd6d-1758a17bce88
2025-06-29 00:56:33 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-06-29 00:56:33 - StateManager - INFO - Using in-memory result for transition transition-LoopNode-*************.
2025-06-29 00:56:33 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-29 00:56:33 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'healthcare', 'iteration_index': 2, 'iteration_metadata': {'timestamp': 273475.852644083, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-29 00:56:33 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-29 00:56:33 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'healthcare', 'iteration_index': 2, 'iteration_metadata': {'timestamp': 273475.852644083, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': healthcare
2025-06-29 00:56:33 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-29 00:56:33 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-29 00:56:33 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-29 00:56:33 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-29 00:56:33 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-29 00:56:33 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-29 00:56:33 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'healthcare', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 00:56:33 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'healthcare', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 00:56:33 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'healthcare', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 00:56:33 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:33 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 11, 'workflow_status': 'running'}
2025-06-29 00:56:33 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 4754364a-701e-42ec-b279-8572a330aa1e) using provided producer.
2025-06-29 00:56:33 - NodeExecutor - DEBUG - Added correlation_id fb09d2c5-7239-440c-bd6d-1758a17bce88 to payload
2025-06-29 00:56:33 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-29 00:56:33 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'healthcare', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '4754364a-701e-42ec-b279-8572a330aa1e', 'correlation_id': 'fb09d2c5-7239-440c-bd6d-1758a17bce88', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-29 00:56:33 - NodeExecutor - DEBUG - Request 4754364a-701e-42ec-b279-8572a330aa1e sent successfully using provided producer.
2025-06-29 00:56:33 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 4754364a-701e-42ec-b279-8572a330aa1e...
2025-06-29 00:56:33 - NodeExecutor - DEBUG - Result consumer received message: Offset=917
2025-06-29 00:56:33 - NodeExecutor - DEBUG - Received valid result for request_id 4754364a-701e-42ec-b279-8572a330aa1e
2025-06-29 00:56:33 - NodeExecutor - INFO - Result received for request 4754364a-701e-42ec-b279-8572a330aa1e.
2025-06-29 00:56:33 - TransitionHandler - INFO - Execution result from Components executor: "healthcare and AI"
2025-06-29 00:56:33 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:33 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'healthcare and AI', 'status': 'completed', 'sequence': 12, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 00:56:33 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'healthcare and AI'}, 'status': 'completed', 'timestamp': **********.80976}}
2025-06-29 00:56:34 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-29 00:56:34 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-29 00:56:34 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 00:56:34 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 00:56:34 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_2', 'loop_iteration_1', 'transition-CombineTextComponent-*************'}
2025-06-29 00:56:34 - LoopExecutor - INFO - 🚨 notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-29 00:56:34 - TransitionHandler - ERROR - ❌ Error notifying loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************: object NoneType can't be used in 'await' expression
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:34 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 4.76 seconds
2025-06-29 00:56:34 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:34 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'result': 'Completed transition in 4.76 seconds', 'message': 'Transition completed in 4.76 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 13, 'workflow_status': 'running'}
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-29 00:56:34 - LoopExecutor - DEBUG - 🔧 Transition transition-CombineTextComponent-************* execution completed, result type: <class 'list'>
2025-06-29 00:56:34 - LoopExecutor - DEBUG - 🔧 Processing result for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:34 - LoopExecutor - DEBUG - 🔗 Transition transition-CombineTextComponent-************* returned next transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:34 - LoopExecutor - DEBUG - 🔧 Extended next_transitions, now: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:34 - LoopExecutor - DEBUG - 🔗 Moving to next transitions in chain: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:34 - LoopExecutor - DEBUG - 🏁 Found exit transitions to execute: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 00:56:34 - LoopExecutor - DEBUG - 🚀 Executing exit transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 00:56:34 - LoopExecutor - ERROR - ❌ Exit transition transition-MCP_script-generation-mcp_script_generate-************* not found in transitions_by_id
2025-06-29 00:56:34 - LoopExecutor - DEBUG - 🔍 Available transition IDs: ['transition-LoopNode-*************', 'transition-CombineTextComponent-*************', 'transition-MergeDataComponent-1751052569332']
2025-06-29 00:56:34 - LoopExecutor - ERROR - ❌ Exit transition transition-MCP_script-generation-mcp_script_generate-************* not found in orchestration engine either
2025-06-29 00:56:34 - LoopExecutor - DEBUG - 🔚 All exit transitions executed, chain complete
2025-06-29 00:56:34 - LoopExecutor - DEBUG - 🔚 Chain execution completed, final_result: None
2025-06-29 00:56:34 - LoopExecutor - INFO - ✅ Iteration 3 completed successfully
2025-06-29 00:56:34 - LoopExecutor - INFO - 🔄 CALLING NEW AGGREGATION METHOD with 3 iteration results
2025-06-29 00:56:34 - LoopExecutor - DEBUG - 🔄 Aggregating 3 iteration results using aggregation_type: collect_all
2025-06-29 00:56:34 - LoopExecutor - ERROR - ❌ Failed to aggregate iteration results: 'NoneType' object has no attribute 'items'
2025-06-29 00:56:34 - LoopExecutor - INFO - 🔄 NEW AGGREGATION METHOD RETURNED: {'error': "Result aggregation failed: 'NoneType' object has no attribute 'items'", 'raw_iteration_results': [{'success': True, 'iteration_index': 0, 'results': None}, {'success': True, 'iteration_index': 1, 'results': None}, {'success': True, 'iteration_index': 2, 'results': None}]}
2025-06-29 00:56:34 - LoopExecutor - INFO - 📊 Loop execution completed with metadata: {'success': True, 'final_results': {'error': "Result aggregation failed: 'NoneType' object has no attribute 'items'", 'raw_iteration_results': [{'success': True, 'iteration_index': 0, 'results': None}, {'success': True, 'iteration_index': 1, 'results': None}, {'success': True, 'iteration_index': 2, 'results': None}]}, 'iteration_count': 3, 'total_iterations': 3}
2025-06-29 00:56:34 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:34 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'status': 'loop_completed', 'metadata': {'success': True, 'final_results': {'error': "Result aggregation failed: 'NoneType' object has no attribute 'items'", 'raw_iteration_results': [{'success': True, 'iteration_index': 0, 'results': None}, {'success': True, 'iteration_index': 1, 'results': None}, {'success': True, 'iteration_index': 2, 'results': None}]}, 'iteration_count': 3, 'total_iterations': 3}, 'transition_id': 'transition-LoopNode-*************', 'sequence': 14, 'workflow_status': 'running'}
2025-06-29 00:56:34 - LoopExecutor - INFO - ✅ Loop node execution completed for transition: transition-LoopNode-*************
2025-06-29 00:56:34 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-29 00:56:34 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-29 00:56:34 - TransitionHandler - INFO - Execution result from loop executor: {
  "final_results": {
    "error": "Result aggregation failed: 'NoneType' object has no attribute 'items'",
    "raw_iteration_results": [
      {
        "success": true,
        "iteration_index": 0,
        "results": null
      },
      {
        "success": true,
        "iteration_index": 1,
        "results": null
      },
      {
        "success": true,
        "iteration_index": 2,
        "results": null
      }
    ]
  }
}
2025-06-29 00:56:34 - TransitionHandler - INFO - Checking execution result for errors: {
  "final_results": {
    "error": "Result aggregation failed: 'NoneType' object has no attribute 'items'",
    "raw_iteration_results": [
      {
        "success": true,
        "iteration_index": 0,
        "results": null
      },
      {
        "success": true,
        "iteration_index": 1,
        "results": null
      },
      {
        "success": true,
        "iteration_index": 2,
        "results": null
      }
    ]
  }
}
2025-06-29 00:56:34 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:34 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition Result received.', 'result': {'final_results': {'error': "Result aggregation failed: 'NoneType' object has no attribute 'items'", 'raw_iteration_results': [{'success': True, 'iteration_index': 0, 'results': None}, {'success': True, 'iteration_index': 1, 'results': None}, {'success': True, 'iteration_index': 2, 'results': None}]}}, 'status': 'completed', 'sequence': 15, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 00:56:34 - TransitionHandler - INFO - 🔄 Handling loop completion for transition: transition-LoopNode-*************
2025-06-29 00:56:34 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'final_results': {'error': "Result aggregation failed: 'NoneType' object has no attribute 'items'", 'raw_iteration_results': [{'success': True, 'iteration_index': 0, 'results': None}, {'success': True, 'iteration_index': 1, 'results': None}, {'success': True, 'iteration_index': 2, 'results': None}]}}
2025-06-29 00:56:35 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-29 00:56:35 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-29 00:56:35 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 00:56:35 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 00:56:35 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-LoopNode-*************', 'current_iteration', 'loop_iteration_0', 'loop_iteration_2', 'loop_iteration_1', 'transition-CombineTextComponent-*************'}
2025-06-29 00:56:35 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-29 00:56:35 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-29 00:56:35 - TransitionHandler - INFO - 🔍 Auto-detected loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-29 00:56:35 - TransitionHandler - DEBUG - 🔍 Loop completion filtering - Loop config: {'iteration_behavior': 'independent', 'iteration_source': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-06-29 00:56:35 - TransitionHandler - DEBUG - 🔍 Loop body transitions to filter: ['transition-CombineTextComponent-*************']
2025-06-29 00:56:35 - TransitionHandler - DEBUG - 🔍 Output data configs: [{'to_transition_id': 'transition-CombineTextComponent-*************', 'target_node_id': 'Combine Text', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'current_item', 'result_path': 'current_item', 'edge_id': 'reactflow__edge-LoopNode-*************current_item-CombineTextComponent-*************main_input'}]}}, {'to_transition_id': 'transition-MergeDataComponent-1751052569332', 'target_node_id': 'Merge Data', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'final_results', 'result_path': 'final_results', 'edge_id': 'reactflow__edge-LoopNode-*************final_results-MergeDataComponent-1751052569332main_input'}]}}]
2025-06-29 00:56:35 - TransitionHandler - DEBUG - 🚫 Skipping loop body transition 'transition-CombineTextComponent-*************' - should have been executed internally by loop executor
2025-06-29 00:56:35 - TransitionHandler - DEBUG - ✅ Adding exit transition 'transition-MergeDataComponent-1751052569332' to next transitions
2025-06-29 00:56:35 - TransitionHandler - INFO - ✅ Loop completion handled for transition-LoopNode-*************. Next transitions: ['transition-MergeDataComponent-1751052569332']
2025-06-29 00:56:35 - TransitionHandler - INFO - Completed transition transition-LoopNode-************* in 26.39 seconds
2025-06-29 00:56:35 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:35 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'result': 'Completed transition in 26.39 seconds', 'message': 'Transition completed in 26.39 seconds', 'transition_id': 'transition-LoopNode-*************', 'status': 'time_logged', 'sequence': 16, 'workflow_status': 'running'}
2025-06-29 00:56:35 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-LoopNode-*************: ['transition-MergeDataComponent-1751052569332']
2025-06-29 00:56:35 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-29 00:56:35 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-MergeDataComponent-1751052569332']]
2025-06-29 00:56:35 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-LoopNode-*************: ['transition-MergeDataComponent-1751052569332']
2025-06-29 00:56:35 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-06-29 00:56:35 - EnhancedWorkflowEngine - INFO - Transition transition-LoopNode-************* completed successfully: 1 next transitions
2025-06-29 00:56:35 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-MergeDataComponent-1751052569332']
2025-06-29 00:56:35 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-MergeDataComponent-1751052569332']
2025-06-29 00:56:35 - EnhancedWorkflowEngine - INFO - Adding transition transition-MergeDataComponent-1751052569332 to pending (all dependencies met)
2025-06-29 00:56:35 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-1751052569332'}
2025-06-29 00:56:36 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:fb09d2c5-7239-440c-bd6d-1758a17bce88'
2025-06-29 00:56:36 - RedisManager - DEBUG - Set key 'workflow_state:fb09d2c5-7239-440c-bd6d-1758a17bce88' with TTL of 600 seconds
2025-06-29 00:56:36 - StateManager - INFO - Workflow state saved to Redis for workflow ID: fb09d2c5-7239-440c-bd6d-1758a17bce88. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 00:56:36 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-29 00:56:36 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-29 00:56:36 - StateManager - INFO - Cleared 1 pending transitions: {'transition-MergeDataComponent-1751052569332'}
2025-06-29 00:56:36 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-29 00:56:36 - StateManager - INFO - Terminated: False
2025-06-29 00:56:36 - StateManager - INFO - Pending transitions (0): []
2025-06-29 00:56:36 - StateManager - INFO - Waiting transitions (0): []
2025-06-29 00:56:36 - StateManager - INFO - Completed transitions (6): ['current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************']
2025-06-29 00:56:36 - StateManager - INFO - Results stored for 6 transitions
2025-06-29 00:56:36 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-29 00:56:36 - StateManager - INFO - Workflow status: inactive
2025-06-29 00:56:36 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-29 00:56:36 - StateManager - INFO - Workflow status: inactive
2025-06-29 00:56:36 - StateManager - INFO - Workflow paused: False
2025-06-29 00:56:36 - StateManager - INFO - ==============================
2025-06-29 00:56:36 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-1751052569332
2025-06-29 00:56:36 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:36 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-1751052569332', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-1751052569332', 'status': 'started', 'sequence': 17, 'workflow_status': 'running'}
2025-06-29 00:56:36 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-1751052569332' (type=standard, execution_type=Components)
2025-06-29 00:56:36 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-29 00:56:36 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-1751052569332
2025-06-29 00:56:36 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-29 00:56:36 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-29 00:56:37 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-29 00:56:37 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-29 00:56:37 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': {'error': "Result aggregation failed: 'NoneType' object has no attribute 'items'", 'raw_iteration_results': [{'success': True, 'iteration_index': 0, 'results': None}, {'success': True, 'iteration_index': 1, 'results': None}, {'success': True, 'iteration_index': 2, 'results': None}]}}
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-29 00:56:37 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-29 00:56:37 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': {'error': "Result aggregation failed: 'NoneType' object has no attribute 'items'", 'raw_iteration_results': [{'success': True, 'iteration_index': 0, 'results': None}, {'success': True, 'iteration_index': 1, 'results': None}, {'success': True, 'iteration_index': 2, 'results': None}]}}
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_results → main_input via path 'final_results': {'error': "Result aggregation failed: 'NoneType' object has no attribute 'items'", 'raw_iteration_results': [{'success': True, 'iteration_index': 0, 'results': None}, {'success': True, 'iteration_index': 1, 'results': None}, {'success': True, 'iteration_index': 2, 'results': None}]}
2025-06-29 00:56:37 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-29 00:56:37 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: []
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-06-29 00:56:37 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-06-29 00:56:37 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 2 fields (22 null/empty fields removed)
2025-06-29 00:56:37 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-29 00:56:37 - TransitionHandler - DEBUG - 📌 Added static parameter: merge_strategy = Aggregate
2025-06-29 00:56:37 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': {'error': "Result aggregation failed: 'NoneType' object has no attribute 'items'", 'raw_iteration_results': [{'success': True, 'iteration_index': 0, 'results': None}, {'success': True, 'iteration_index': 1, 'results': None}, {'success': True, 'iteration_index': 2, 'results': None}]}, 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-29 00:56:37 - TransitionHandler - DEBUG - tool Parameters: {'main_input': {'error': "Result aggregation failed: 'NoneType' object has no attribute 'items'", 'raw_iteration_results': [{'success': True, 'iteration_index': 0, 'results': None}, {'success': True, 'iteration_index': 1, 'results': None}, {'success': True, 'iteration_index': 2, 'results': None}]}, 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-29 00:56:37 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-1751052569332' with parameters: {'main_input': {'error': "Result aggregation failed: 'NoneType' object has no attribute 'items'", 'raw_iteration_results': [{'success': True, 'iteration_index': 0, 'results': None}, {'success': True, 'iteration_index': 1, 'results': None}, {'success': True, 'iteration_index': 2, 'results': None}]}, 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-29 00:56:37 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:37 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'transition_id': 'transition-MergeDataComponent-1751052569332', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server MergeDataComponent', 'status': 'connecting', 'sequence': 18, 'workflow_status': 'running'}
2025-06-29 00:56:37 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: ce16def7-8996-4f57-9a47-36a74570bf72) using provided producer.
2025-06-29 00:56:37 - NodeExecutor - DEBUG - Added correlation_id fb09d2c5-7239-440c-bd6d-1758a17bce88 to payload
2025-06-29 00:56:37 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-1751052569332 to payload
2025-06-29 00:56:37 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': {'error': "Result aggregation failed: 'NoneType' object has no attribute 'items'", 'raw_iteration_results': [{'success': True, 'iteration_index': 0, 'results': None}, {'success': True, 'iteration_index': 1, 'results': None}, {'success': True, 'iteration_index': 2, 'results': None}]}, 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}, 'request_id': 'ce16def7-8996-4f57-9a47-36a74570bf72', 'correlation_id': 'fb09d2c5-7239-440c-bd6d-1758a17bce88', 'transition_id': 'transition-MergeDataComponent-1751052569332'}
2025-06-29 00:56:37 - NodeExecutor - DEBUG - Request ce16def7-8996-4f57-9a47-36a74570bf72 sent successfully using provided producer.
2025-06-29 00:56:37 - NodeExecutor - DEBUG - Waiting indefinitely for result for request ce16def7-8996-4f57-9a47-36a74570bf72...
2025-06-29 00:56:38 - NodeExecutor - DEBUG - Result consumer received message: Offset=918
2025-06-29 00:56:38 - NodeExecutor - WARNING - Received result with error field for request_id ce16def7-8996-4f57-9a47-36a74570bf72: Result aggregation failed: 'NoneType' object has no attribute 'items'
2025-06-29 00:56:38 - NodeExecutor - ERROR - Error during node execution ce16def7-8996-4f57-9a47-36a74570bf72: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'
2025-06-29 00:56:38 - TransitionHandler - ERROR - Tool execution failed for tool 'MergeDataComponent' (tool_id: 1) in node 'MergeDataComponent' of transition 'transition-MergeDataComponent-1751052569332': Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'

2025-06-29 00:56:38 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id fb09d2c5-7239-440c-bd6d-1758a17bce88):
2025-06-29 00:56:38 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'transition_id': 'transition-MergeDataComponent-1751052569332', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Transition faced an error during execution.', 'result': "[ERROR] Tool Execution Failed with error: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'", 'status': 'failed', 'sequence': 19, 'workflow_status': 'running'}
2025-06-29 00:56:38 - TransitionHandler - ERROR - Exception in transition transition-MergeDataComponent-1751052569332: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'
2025-06-29 00:56:38 - EnhancedWorkflowEngine - DEBUG - Results: [Exception("Exception in transition transition-MergeDataComponent-1751052569332: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'")]
2025-06-29 00:56:38 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-MergeDataComponent-1751052569332: Exception in transition transition-MergeDataComponent-1751052569332: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'
2025-06-29 00:56:38 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'Exception'>, is_list: False
2025-06-29 00:56:38 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-MergeDataComponent-1751052569332: Exception in transition transition-MergeDataComponent-1751052569332: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'
2025-06-29 00:56:38 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-MergeDataComponent-1751052569332: NoneType: None

2025-06-29 00:56:38 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-MergeDataComponent-1751052569332: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'
2025-06-29 00:56:38 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-MergeDataComponent-1751052569332: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'

2025-06-29 00:56:38 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-MergeDataComponent-1751052569332: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'
2025-06-29 00:56:38 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-MergeDataComponent-1751052569332: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'

2025-06-29 00:56:38 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-MergeDataComponent-1751052569332: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'
2025-06-29 00:56:38 - KafkaWorkflowConsumer - INFO - Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' final status: failed, result: Exception in workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43': Exception in transition transition-MergeDataComponent-1751052569332: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'
2025-06-29 00:56:38 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: fb09d2c5-7239-440c-bd6d-1758a17bce88, response: {'status': 'failed', 'result': "Exception in workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43': Exception in transition transition-MergeDataComponent-1751052569332: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'", 'workflow_status': 'failed', 'error': "Exception in transition transition-MergeDataComponent-1751052569332: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Result aggregation failed: 'NoneType' object has no attribute 'items'", 'error_type': 'Exception'}
2025-06-29 00:56:38 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: fb09d2c5-7239-440c-bd6d-1758a17bce88 
2025-06-29 00:57:08 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-29 00:57:08 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-29 00:57:08 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-29 00:57:08 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-29 00:57:08 - Main - ERROR - Shutting down due to keyboard interrupt...
