2025-06-28 22:41:57 - Main - INFO - Starting Server
2025-06-28 22:41:57 - Main - INFO - Connection at: **************:9092
2025-06-28 22:41:57 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 22:41:57 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-28 22:41:57 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 22:41:57 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 22:41:57 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 22:41:58 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 22:41:58 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 22:42:00 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 22:42:02 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-28 22:42:02 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-28 22:42:04 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 22:42:05 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-28 22:42:05 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 22:42:07 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 22:42:07 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 22:42:08 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 22:42:08 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-28 22:42:08 - RedisEventListener - INFO - Redis event listener started
2025-06-28 22:42:08 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-28 22:42:08 - StateManager - DEBUG - Using provided database connections
2025-06-28 22:42:08 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 22:42:08 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 22:42:08 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 22:42:09 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-28 22:42:09 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 22:42:09 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 22:42:09 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-28 22:42:09 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-28 22:42:09 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-28 22:42:09 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-28 22:42:12 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-28 22:42:12 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-28 22:42:12 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-28 22:42:25 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-28 22:42:32 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-28 22:42:32 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-28 22:42:32 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-28 22:42:38 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-28 22:42:38 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-28 22:42:38 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-28 22:42:44 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-28 22:42:44 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-28 22:42:44 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1022
2025-06-28 22:42:44 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': **********, 'task_type': 'workflow', 'data': {'workflow_id': 'feda07bf-a91e-4004-80cb-72416cdb5a43', 'payload': {'user_dependent_fields': ['iteration_list'], 'user_payload_template': {'iteration_list': {'value': '["blockchain","automation","healthcare"]', 'transition_id': 'LoopNode-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-06-28 22:42:44 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 22:42:44 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 22:42:45 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-28 22:42:45 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow LOOP TEST DO NOT DELETE retrieved successfully",
  "workflow": {
    "id": "feda07bf-a91e-4004-80cb-72416cdb5a43",
    "name": "LOOP TEST DO NOT DELETE",
    "description": "LOOP_TEST_DO_NOT_DELETE",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/16a976d6-6669-4cb4-8207-4f9b8626a0b8.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/91cb74a1-1bfd-42fa-b59d-58773b35d33a.json",
    "start_nodes": [
      {
        "field": "iteration_list",
        "type": "list",
        "transition_id": "transition-LoopNode-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-24T11:13:46.129953",
    "updated_at": "2025-06-28T14:13:59.013729",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "LoopNode",
        "display_name": "For Each Loop",
        "type": "component",
        "transition_id": "transition-LoopNode-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1751052569332"
      },
      {
        "name": "MCP_script-generation-mcp_script_generate",
        "id": "748a8221-d7d9-4352-93ae-00700f4d28b1",
        "transition_id": "transition-MCP_script-generation-mcp_script_generate-*************",
        "type": "mcp",
        "display_name": "script-generation-mcp - script_generate",
        "data": {
          "input_schema": {
            "$defs": {
              "Keywords": {
                "properties": {
                  "time": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Time"
                  },
                  "objective": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Objective"
                  },
                  "audience": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Audience"
                  },
                  "gender": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Gender"
                  },
                  "tone": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Tone"
                  },
                  "speakers": {
                    "anyOf": [
                      {
                        "items": {
                          "type": "string"
                        },
                        "type": "array"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Speakers"
                  }
                },
                "title": "Keywords",
                "type": "object"
              },
              "ScriptType": {
                "enum": [
                  "VIDEO",
                  "TOPIC",
                  "SCRIPT",
                  "BLOG",
                  "AI"
                ],
                "title": "ScriptType",
                "type": "string"
              },
              "VideoType": {
                "enum": [
                  "SHORT",
                  "LONG"
                ],
                "title": "VideoType",
                "type": "string"
              }
            },
            "properties": {
              "topic": {
                "title": "Topic",
                "type": "string"
              },
              "script_type": {
                "$ref": "#/$defs/ScriptType",
                "default": "TOPIC"
              },
              "keywords": {
                "$ref": "#/$defs/Keywords"
              },
              "video_type": {
                "$ref": "#/$defs/VideoType",
                "default": "SHORT"
              },
              "link": {
                "anyOf": [
                  {
                    "format": "uri",
                    "maxLength": 2083,
                    "minLength": 1,
                    "type": "string"
                  },
                  {
                    "type": "null"
                  }
                ],
                "default": null,
                "title": "Link"
              }
            },
            "required": [
              "topic"
            ],
            "title": "GenerateScriptInput",
            "type": "object"
          },
          "output_schema": {
            "properties": {
              "title": {
                "type": "string",
                "description": "Title of the generated script",
                "title": "title"
              },
              "script": {
                "type": "string",
                "description": "The generated script",
                "title": "script"
              },
              "script_type": {
                "type": "string",
                "description": "Type of the script",
                "title": "script_type"
              },
              "video_type": {
                "type": "string",
                "description": "The type of video",
                "title": "video_type"
              },
              "link": {
                "type": "string",
                "format": "uri",
                "description": "Optional link for the script",
                "title": "link"
              }
            }
          }
        }
      }
    ],
    "is_updated": true
  }
}
2025-06-28 22:42:45 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for feda07bf-a91e-4004-80cb-72416cdb5a43 - server_script_path is optional
2025-06-28 22:42:45 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-28 22:42:45 - StateManager - DEBUG - Using global database connections from initializer
2025-06-28 22:42:45 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 22:42:45 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 22:42:45 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 22:42:46 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 22:42:46 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 22:42:46 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-28 22:42:46 - StateManager - DEBUG - Using provided database connections
2025-06-28 22:42:46 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 22:42:46 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 22:42:46 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 22:42:47 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 22:42:47 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 22:42:47 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-28 22:42:47 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751052569332: ['transition-LoopNode-*************']
2025-06-28 22:42:47 - StateManager - INFO - Built dependency map for 3 transitions
2025-06-28 22:42:47 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-28 22:42:47 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751052569332 depends on: ['transition-LoopNode-*************']
2025-06-28 22:42:47 - MCPToolExecutor - DEBUG - Set correlation ID to: de3f7fb6-026e-45a8-9989-cd7602400a0f
2025-06-28 22:42:47 - EnhancedWorkflowEngine - DEBUG - Set correlation_id de3f7fb6-026e-45a8-9989-cd7602400a0f in tool_executor
2025-06-28 22:42:47 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 22:42:47 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-06-28 22:42:47 - NodeExecutor - DEBUG - Set correlation ID to: de3f7fb6-026e-45a8-9989-cd7602400a0f
2025-06-28 22:42:47 - EnhancedWorkflowEngine - DEBUG - Set correlation_id de3f7fb6-026e-45a8-9989-cd7602400a0f in node_executor
2025-06-28 22:42:47 - AgentExecutor - DEBUG - Set correlation ID to: de3f7fb6-026e-45a8-9989-cd7602400a0f
2025-06-28 22:42:47 - EnhancedWorkflowEngine - DEBUG - Set correlation_id de3f7fb6-026e-45a8-9989-cd7602400a0f in agent_executor
2025-06-28 22:42:47 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 22:42:47 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-06-28 22:42:47 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-28 22:42:47 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: de3f7fb6-026e-45a8-9989-cd7602400a0f
2025-06-28 22:42:47 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: de3f7fb6-026e-45a8-9989-cd7602400a0f
2025-06-28 22:42:47 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: de3f7fb6-026e-45a8-9989-cd7602400a0f, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-28 22:42:47 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 22:42:47 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 22:42:47 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 22:42:47 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 22:42:48 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:de3f7fb6-026e-45a8-9989-cd7602400a0f'
2025-06-28 22:42:48 - RedisManager - DEBUG - Set key 'workflow_state:de3f7fb6-026e-45a8-9989-cd7602400a0f' with TTL of 600 seconds
2025-06-28 22:42:48 - StateManager - INFO - Workflow state saved to Redis for workflow ID: de3f7fb6-026e-45a8-9989-cd7602400a0f. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 22:42:48 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 22:42:48 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 22:42:48 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 22:42:48 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 22:42:48 - StateManager - INFO - Terminated: False
2025-06-28 22:42:48 - StateManager - INFO - Pending transitions (0): []
2025-06-28 22:42:48 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 22:42:48 - StateManager - INFO - Completed transitions (0): []
2025-06-28 22:42:48 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 22:42:48 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 22:42:48 - StateManager - INFO - Workflow status: inactive
2025-06-28 22:42:48 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 22:42:48 - StateManager - INFO - Workflow status: inactive
2025-06-28 22:42:48 - StateManager - INFO - Workflow paused: False
2025-06-28 22:42:48 - StateManager - INFO - ==============================
2025-06-28 22:42:48 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 22:42:48 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id de3f7fb6-026e-45a8-9989-cd7602400a0f):
2025-06-28 22:42:48 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: de3f7fb6-026e-45a8-9989-cd7602400a0f, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-28 22:42:48 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 22:42:48 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-28 22:42:48 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 22:42:48 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 22:42:48 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 22:42:48 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 22:42:48 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 22:42:48 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 22:42:48 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 22:42:48 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 22:42:48 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id de3f7fb6-026e-45a8-9989-cd7602400a0f):
2025-06-28 22:42:48 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: de3f7fb6-026e-45a8-9989-cd7602400a0f, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-28 22:42:48 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 22:42:48 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 22:42:48 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 22:42:48 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 22:42:48 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 22:42:48 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 22:42:48 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-28 22:42:48 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-28 22:42:48 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-28 22:42:48 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-28 22:42:48 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-28 22:42:48 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 22:42:48 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-28 22:42:48 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-28 22:42:48 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-28 22:42:48 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-28 22:42:48 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-28 22:42:48 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-28 22:42:48 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-28 22:42:48 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-28 22:42:48 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 22:42:48 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-28 22:42:48 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain
2025-06-28 22:42:48 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 22:42:49 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 22:42:49 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 22:42:49 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 22:42:49 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 22:42:49 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 22:42:49 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 22:42:49 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 22:42:50 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 22:42:50 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 22:42:50 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 22:42:50 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0'}
2025-06-28 22:42:50 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 265469.354308625, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 22:42:51 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 22:42:51 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 22:42:51 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 22:42:51 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 22:42:51 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 22:42:51 - LoopExecutor - DEBUG - 💾 Stored loop iteration data under transition ID: transition-LoopNode-*************
2025-06-28 22:42:51 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-28 22:42:51 - LoopExecutor - DEBUG - 🚀 Starting entry transition: transition-CombineTextComponent-*************
2025-06-28 22:42:51 - LoopExecutor - DEBUG - 🎯 Executing transition transition-CombineTextComponent-************* through orchestration engine
2025-06-28 22:42:51 - LoopExecutor - INFO - 🚀 REAL EXECUTION: Starting transition-CombineTextComponent-************* via transition handler
2025-06-28 22:42:51 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 22:42:51 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id de3f7fb6-026e-45a8-9989-cd7602400a0f):
2025-06-28 22:42:51 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: de3f7fb6-026e-45a8-9989-cd7602400a0f, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 2, 'workflow_status': 'running'}
2025-06-28 22:42:51 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 22:42:51 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 22:42:51 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 22:42:51 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 22:42:51 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 22:42:52 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 22:42:52 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 22:42:52 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 265469.354308625, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 22:42:52 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 22:42:52 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 265469.354308625, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-28 22:42:52 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 22:42:52 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 22:42:52 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 22:42:52 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 22:42:52 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 22:42:52 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 22:42:52 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 22:42:52 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 22:42:52 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 22:42:52 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id de3f7fb6-026e-45a8-9989-cd7602400a0f):
2025-06-28 22:42:52 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: de3f7fb6-026e-45a8-9989-cd7602400a0f, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 3, 'workflow_status': 'running'}
2025-06-28 22:42:52 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 00904e01-4808-44a6-a3a5-bab166f4d9ce) using provided producer.
2025-06-28 22:42:52 - NodeExecutor - DEBUG - Added correlation_id de3f7fb6-026e-45a8-9989-cd7602400a0f to payload
2025-06-28 22:42:52 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 22:42:52 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '00904e01-4808-44a6-a3a5-bab166f4d9ce', 'correlation_id': 'de3f7fb6-026e-45a8-9989-cd7602400a0f', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 22:42:52 - NodeExecutor - DEBUG - Request 00904e01-4808-44a6-a3a5-bab166f4d9ce sent successfully using provided producer.
2025-06-28 22:42:52 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 00904e01-4808-44a6-a3a5-bab166f4d9ce...
2025-06-28 22:42:52 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1022, corr_id: de3f7fb6-026e-45a8-9989-cd7602400a0f
2025-06-28 22:42:53 - NodeExecutor - DEBUG - Result consumer received message: Offset=877
2025-06-28 22:42:53 - NodeExecutor - DEBUG - Received valid result for request_id 00904e01-4808-44a6-a3a5-bab166f4d9ce
2025-06-28 22:42:53 - NodeExecutor - INFO - Result received for request 00904e01-4808-44a6-a3a5-bab166f4d9ce.
2025-06-28 22:42:53 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-28 22:42:53 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id de3f7fb6-026e-45a8-9989-cd7602400a0f):
2025-06-28 22:42:53 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: de3f7fb6-026e-45a8-9989-cd7602400a0f, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 4, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 22:42:53 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751130773.2627358}}
2025-06-28 22:42:53 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 22:42:54 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 22:42:54 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 22:42:54 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 22:42:54 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 22:42:54 - LoopExecutor - INFO - 🚨 async notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-28 22:42:54 - LoopExecutor - DEBUG - 🔔 Received completion notification for transition: transition-CombineTextComponent-*************
2025-06-28 22:42:54 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 22:42:54 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.67 seconds
2025-06-28 22:42:54 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id de3f7fb6-026e-45a8-9989-cd7602400a0f):
2025-06-28 22:42:54 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: de3f7fb6-026e-45a8-9989-cd7602400a0f, response: {'result': 'Completed transition in 2.67 seconds', 'message': 'Transition completed in 2.67 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 5, 'workflow_status': 'running'}
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 22:42:54 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-28 22:42:54 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 22:42:54 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 22:43:05 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 22:43:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 22:43:06 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 22:43:06 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 22:43:17 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-28 22:43:17 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-28 22:43:17 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-28 22:43:17 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-28 22:43:17 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-28 22:43:17 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-28 22:43:17 - EnhancedWorkflowEngine - WARNING - Workflow de3f7fb6-026e-45a8-9989-cd7602400a0f execution was cancelled!
2025-06-28 22:43:17 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 22:43:17 - KafkaWorkflowConsumer - WARNING - Workflow execution for 'feda07bf-a91e-4004-80cb-72416cdb5a43' was cancelled
2025-06-28 22:43:17 - KafkaWorkflowConsumer - INFO - Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' final status: cancelled, result: Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' execution was cancelled
2025-06-28 22:43:17 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: de3f7fb6-026e-45a8-9989-cd7602400a0f, response: {'status': 'Workflow Cancelled', 'result': "Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' execution was cancelled", 'workflow_status': 'cancelled'}
2025-06-28 22:43:17 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: de3f7fb6-026e-45a8-9989-cd7602400a0f 
2025-06-28 22:43:17 - Main - ERROR - Shutting down due to keyboard interrupt...
