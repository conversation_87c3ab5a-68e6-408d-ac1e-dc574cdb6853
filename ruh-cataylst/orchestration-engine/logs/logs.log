2025-06-29 02:25:45 - Main - INFO - Starting Server
2025-06-29 02:25:45 - Main - INFO - Connection at: **************:9092
2025-06-29 02:25:45 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-29 02:25:45 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-29 02:25:45 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-29 02:25:45 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-29 02:25:45 - Red<PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-29 02:25:46 - Red<PERSON>Manager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-29 02:25:46 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-29 02:25:48 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - Successfully connected to Redis on DB index: 6!
