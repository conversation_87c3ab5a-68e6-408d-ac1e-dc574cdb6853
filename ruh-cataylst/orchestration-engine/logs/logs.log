2025-06-28 18:47:37 - Main - INFO - Starting Server
2025-06-28 18:47:41 - MCP<PERSON>oolExecutor - INFO - Background result consumer loop started.
2025-06-28 18:47:41 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-28 18:47:47 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-28 18:47:47 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-28 18:47:47 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-28 18:47:54 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-28 18:47:54 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-28 18:47:58 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1008
2025-06-28 18:47:58 - Ka<PERSON><PERSON><PERSON>orkflowConsumer - DEBUG - message json: {'task_id': **********, 'task_type': 'workflow', 'data': {'workflow_id': 'feda07bf-a91e-4004-80cb-72416cdb5a43', 'payload': {'user_dependent_fields': ['iteration_list'], 'user_payload_template': {'iteration_list': {'value': '["blockchain","automation","healthcare"]', 'transition_id': 'LoopNode-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-06-28 18:47:58 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 18:47:58 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 18:47:59 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-28 18:47:59 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow LOOP TEST DO NOT DELETE retrieved successfully",
  "workflow": {
    "id": "feda07bf-a91e-4004-80cb-72416cdb5a43",
    "name": "LOOP TEST DO NOT DELETE",
    "description": "LOOP_TEST_DO_NOT_DELETE",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/dfbb35aa-5dac-4505-b8ab-e29a2853971a.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/e1da74c2-0744-4dc2-b2c1-d46aca8a8f34.json",
    "start_nodes": [
      {
        "field": "iteration_list",
        "type": "list",
        "transition_id": "transition-LoopNode-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-24T11:13:46.129953",
    "updated_at": "2025-06-28T13:04:23.189580",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "LoopNode",
        "display_name": "For Each Loop",
        "type": "component",
        "transition_id": "transition-LoopNode-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************"
      },
      {
        "name": "MCP_script-generation-mcp_script_generate",
        "id": "748a8221-d7d9-4352-93ae-00700f4d28b1",
        "transition_id": "transition-MCP_script-generation-mcp_script_generate-1751115762780",
        "type": "mcp",
        "display_name": "script-generation-mcp - script_generate",
        "data": {
          "input_schema": {
            "$defs": {
              "Keywords": {
                "properties": {
                  "time": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Time"
                  },
                  "objective": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Objective"
                  },
                  "audience": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Audience"
                  },
                  "gender": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Gender"
                  },
                  "tone": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Tone"
                  },
                  "speakers": {
                    "anyOf": [
                      {
                        "items": {
                          "type": "string"
                        },
                        "type": "array"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Speakers"
                  }
                },
                "title": "Keywords",
                "type": "object"
              },
              "ScriptType": {
                "enum": [
                  "VIDEO",
                  "TOPIC",
                  "SCRIPT",
                  "BLOG",
                  "AI"
                ],
                "title": "ScriptType",
                "type": "string"
              },
              "VideoType": {
                "enum": [
                  "SHORT",
                  "LONG"
                ],
                "title": "VideoType",
                "type": "string"
              }
            },
            "properties": {
              "topic": {
                "title": "Topic",
                "type": "string"
              },
              "script_type": {
                "$ref": "#/$defs/ScriptType",
                "default": "TOPIC"
              },
              "keywords": {
                "$ref": "#/$defs/Keywords"
              },
              "video_type": {
                "$ref": "#/$defs/VideoType",
                "default": "SHORT"
              },
              "link": {
                "anyOf": [
                  {
                    "format": "uri",
                    "maxLength": 2083,
                    "minLength": 1,
                    "type": "string"
                  },
                  {
                    "type": "null"
                  }
                ],
                "default": null,
                "title": "Link"
              }
            },
            "required": [
              "topic"
            ],
            "title": "GenerateScriptInput",
            "type": "object"
          },
          "output_schema": {
            "properties": {
              "title": {
                "type": "string",
                "description": "Title of the generated script",
                "title": "title"
              },
              "script": {
                "type": "string",
                "description": "The generated script",
                "title": "script"
              },
              "script_type": {
                "type": "string",
                "description": "Type of the script",
                "title": "script_type"
              },
              "video_type": {
                "type": "string",
                "description": "The type of video",
                "title": "video_type"
              },
              "link": {
                "type": "string",
                "format": "uri",
                "description": "Optional link for the script",
                "title": "link"
              }
            }
          }
        }
      }
    ],
    "is_updated": true
  }
}
2025-06-28 18:47:59 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for feda07bf-a91e-4004-80cb-72416cdb5a43 - server_script_path is optional
2025-06-28 18:47:59 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-28 18:47:59 - StateManager - DEBUG - Using global database connections from initializer
2025-06-28 18:47:59 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 18:47:59 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 18:47:59 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 18:48:00 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 18:48:00 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 18:48:00 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-28 18:48:00 - StateManager - DEBUG - Using provided database connections
2025-06-28 18:48:00 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 18:48:00 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 18:48:00 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 18:48:01 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 18:48:01 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 18:48:01 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-*************: ['transition-LoopNode-*************']
2025-06-28 18:48:01 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-28 18:48:01 - StateManager - DEBUG - Extracted dependencies for transition transition-MCP_script-generation-mcp_script_generate-1751115762780: ['transition-CombineTextComponent-*************']
2025-06-28 18:48:01 - StateManager - INFO - Built dependency map for 4 transitions
2025-06-28 18:48:01 - StateManager - DEBUG - Transition transition-MergeDataComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-28 18:48:01 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-28 18:48:01 - StateManager - DEBUG - Transition transition-MCP_script-generation-mcp_script_generate-1751115762780 depends on: ['transition-CombineTextComponent-*************']
2025-06-28 18:48:01 - MCPToolExecutor - DEBUG - Set correlation ID to: 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 18:48:01 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 0dece4c1-d537-423d-89d2-30fe493c7758 in tool_executor
2025-06-28 18:48:01 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 18:48:01 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-06-28 18:48:01 - NodeExecutor - DEBUG - Set correlation ID to: 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 18:48:01 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 0dece4c1-d537-423d-89d2-30fe493c7758 in node_executor
2025-06-28 18:48:01 - AgentExecutor - DEBUG - Set correlation ID to: 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 18:48:01 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 0dece4c1-d537-423d-89d2-30fe493c7758 in agent_executor
2025-06-28 18:48:01 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 18:48:01 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-06-28 18:48:01 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-28 18:48:01 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 18:48:01 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 18:48:01 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-28 18:48:01 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 18:48:01 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 18:48:01 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 18:48:01 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 18:48:02 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:0dece4c1-d537-423d-89d2-30fe493c7758'
2025-06-28 18:48:03 - RedisManager - DEBUG - Set key 'workflow_state:0dece4c1-d537-423d-89d2-30fe493c7758' with TTL of 600 seconds
2025-06-28 18:48:03 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 0dece4c1-d537-423d-89d2-30fe493c7758. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:03 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 18:48:03 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 18:48:03 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 18:48:03 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 18:48:03 - StateManager - INFO - Terminated: False
2025-06-28 18:48:03 - StateManager - INFO - Pending transitions (0): []
2025-06-28 18:48:03 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 18:48:03 - StateManager - INFO - Completed transitions (0): []
2025-06-28 18:48:03 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 18:48:03 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 18:48:03 - StateManager - INFO - Workflow status: inactive
2025-06-28 18:48:03 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 18:48:03 - StateManager - INFO - Workflow status: inactive
2025-06-28 18:48:03 - StateManager - INFO - Workflow paused: False
2025-06-28 18:48:03 - StateManager - INFO - ==============================
2025-06-28 18:48:03 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 18:48:03 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:03 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-28 18:48:03 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 18:48:03 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 18:48:03 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 18:48:03 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 18:48:03 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 18:48:03 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 18:48:03 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 18:48:03 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 18:48:03 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 18:48:03 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:03 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-28 18:48:03 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 18:48:03 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 18:48:03 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 18:48:03 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 18:48:03 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-28 18:48:03 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 18:48:03 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 18:48:03 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 18:48:03 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 18:48:04 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 18:48:04 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:04 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 18:48:04 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 18:48:04 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 18:48:04 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 18:48:05 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 18:48:05 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:05 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 18:48:05 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0'}
2025-06-28 18:48:05 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 251852.106931083, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 18:48:05 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 18:48:06 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 18:48:06 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:06 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 18:48:06 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-LoopNode-*************', 'current_iteration', 'loop_iteration_0'}
2025-06-28 18:48:06 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 18:48:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 2, 'workflow_status': 'running'}
2025-06-28 18:48:06 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 18:48:06 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 18:48:06 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 18:48:06 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 18:48:06 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 18:48:06 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 18:48:06 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 18:48:06 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 251852.106931083, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 18:48:06 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 18:48:06 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 251852.106931083, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-28 18:48:06 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 18:48:06 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 18:48:06 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 18:48:06 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 18:48:06 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 18:48:06 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 18:48:06 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 18:48:06 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 18:48:06 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 18:48:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 3, 'workflow_status': 'running'}
2025-06-28 18:48:06 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 8c56a569-2725-475c-8a3e-f8e514d3998d) using provided producer.
2025-06-28 18:48:06 - NodeExecutor - DEBUG - Added correlation_id 0dece4c1-d537-423d-89d2-30fe493c7758 to payload
2025-06-28 18:48:06 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 18:48:06 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '8c56a569-2725-475c-8a3e-f8e514d3998d', 'correlation_id': '0dece4c1-d537-423d-89d2-30fe493c7758', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 18:48:06 - NodeExecutor - DEBUG - Request 8c56a569-2725-475c-8a3e-f8e514d3998d sent successfully using provided producer.
2025-06-28 18:48:06 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 8c56a569-2725-475c-8a3e-f8e514d3998d...
2025-06-28 18:48:06 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1008, corr_id: 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 18:48:08 - NodeExecutor - DEBUG - Result consumer received message: Offset=857
2025-06-28 18:48:08 - NodeExecutor - DEBUG - Received valid result for request_id 8c56a569-2725-475c-8a3e-f8e514d3998d
2025-06-28 18:48:08 - NodeExecutor - INFO - Result received for request 8c56a569-2725-475c-8a3e-f8e514d3998d.
2025-06-28 18:48:08 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-28 18:48:08 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 4, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 18:48:08 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751116688.484691}}
2025-06-28 18:48:08 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 18:48:09 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 18:48:09 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:09 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 18:48:09 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-LoopNode-*************', 'current_iteration', 'transition-CombineTextComponent-*************', 'loop_iteration_0'}
2025-06-28 18:48:09 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 18:48:09 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 18:48:09 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 18:48:09 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 18:48:09 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 18:48:09 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 18:48:09 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 18:48:09 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 18:48:09 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 18:48:09 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 3.21 seconds
2025-06-28 18:48:09 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:09 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'result': 'Completed transition in 3.21 seconds', 'message': 'Transition completed in 3.21 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 5, 'workflow_status': 'running'}
2025-06-28 18:48:09 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in memory: automation
2025-06-28 18:48:09 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_1
2025-06-28 18:48:10 - RedisManager - DEBUG - Set key 'result:loop_iteration_1' with TTL of 900 seconds
2025-06-28 18:48:10 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:10 - StateManager - INFO - Marked transition loop_iteration_1 as completed (was_pending=False, was_waiting=False)
2025-06-28 18:48:10 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'loop_iteration_1', 'transition-LoopNode-*************', 'current_iteration'}
2025-06-28 18:48:10 - StateManager - DEBUG - Stored result for transition current_iteration in memory: automation
2025-06-28 18:48:10 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 18:48:10 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 18:48:10 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:10 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 18:48:10 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'loop_iteration_1', 'transition-LoopNode-*************', 'current_iteration'}
2025-06-28 18:48:10 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'automation', 'iteration_index': 1, 'iteration_metadata': {'timestamp': 251857.773101958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 18:48:11 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 18:48:11 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 18:48:11 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:11 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 18:48:11 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'loop_iteration_1', 'transition-LoopNode-*************', 'current_iteration'}
2025-06-28 18:48:11 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 18:48:11 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:11 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-06-28 18:48:11 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 18:48:11 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 18:48:11 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 18:48:11 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 18:48:11 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 18:48:12 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 18:48:12 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 18:48:12 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'automation', 'iteration_index': 1, 'iteration_metadata': {'timestamp': 251857.773101958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 18:48:12 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 18:48:12 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'automation', 'iteration_index': 1, 'iteration_metadata': {'timestamp': 251857.773101958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': automation
2025-06-28 18:48:12 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 18:48:12 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 18:48:12 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 18:48:12 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 18:48:12 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 18:48:12 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 18:48:12 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'automation', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 18:48:12 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'automation', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 18:48:12 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'automation', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 18:48:12 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:12 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 7, 'workflow_status': 'running'}
2025-06-28 18:48:12 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: ee279f77-94fd-4992-b710-2426b5168b59) using provided producer.
2025-06-28 18:48:12 - NodeExecutor - DEBUG - Added correlation_id 0dece4c1-d537-423d-89d2-30fe493c7758 to payload
2025-06-28 18:48:12 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 18:48:12 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'automation', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': 'ee279f77-94fd-4992-b710-2426b5168b59', 'correlation_id': '0dece4c1-d537-423d-89d2-30fe493c7758', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 18:48:12 - NodeExecutor - DEBUG - Request ee279f77-94fd-4992-b710-2426b5168b59 sent successfully using provided producer.
2025-06-28 18:48:12 - NodeExecutor - DEBUG - Waiting indefinitely for result for request ee279f77-94fd-4992-b710-2426b5168b59...
2025-06-28 18:48:13 - NodeExecutor - DEBUG - Result consumer received message: Offset=858
2025-06-28 18:48:13 - NodeExecutor - DEBUG - Received valid result for request_id ee279f77-94fd-4992-b710-2426b5168b59
2025-06-28 18:48:13 - NodeExecutor - INFO - Result received for request ee279f77-94fd-4992-b710-2426b5168b59.
2025-06-28 18:48:13 - TransitionHandler - INFO - Execution result from Components executor: "automation and AI"
2025-06-28 18:48:13 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:13 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'automation and AI', 'status': 'completed', 'sequence': 8, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 18:48:13 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'automation and AI'}, 'status': 'completed', 'timestamp': 1751116693.095608}}
2025-06-28 18:48:13 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 18:48:14 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 18:48:14 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:14 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 18:48:14 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'loop_iteration_1', 'transition-LoopNode-*************', 'current_iteration'}
2025-06-28 18:48:14 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 18:48:14 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 18:48:14 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 18:48:14 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 18:48:14 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 18:48:14 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 18:48:14 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 18:48:14 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 18:48:14 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 18:48:14 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.55 seconds
2025-06-28 18:48:14 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:14 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'result': 'Completed transition in 2.55 seconds', 'message': 'Transition completed in 2.55 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 9, 'workflow_status': 'running'}
2025-06-28 18:48:14 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in memory: healthcare
2025-06-28 18:48:14 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:48:14 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_2
2025-06-28 18:48:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:48:15 - RedisManager - DEBUG - Set key 'result:loop_iteration_2' with TTL of 900 seconds
2025-06-28 18:48:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:48:15 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 18:48:15 - StateManager - INFO - Marked transition loop_iteration_2 as completed (was_pending=False, was_waiting=False)
2025-06-28 18:48:15 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'transition-LoopNode-*************', 'current_iteration'}
2025-06-28 18:48:15 - StateManager - DEBUG - Stored result for transition current_iteration in memory: healthcare
2025-06-28 18:48:15 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 18:48:15 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 18:48:15 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:15 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 18:48:15 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'transition-LoopNode-*************', 'current_iteration'}
2025-06-28 18:48:15 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'healthcare', 'iteration_index': 2, 'iteration_metadata': {'timestamp': 251862.799647291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 18:48:16 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 18:48:16 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 18:48:16 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:16 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 18:48:16 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'transition-LoopNode-*************', 'current_iteration'}
2025-06-28 18:48:16 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 18:48:16 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:16 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 10, 'workflow_status': 'running'}
2025-06-28 18:48:16 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 18:48:16 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 18:48:16 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 18:48:16 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 18:48:16 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 18:48:17 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 18:48:17 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 18:48:17 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'healthcare', 'iteration_index': 2, 'iteration_metadata': {'timestamp': 251862.799647291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 18:48:17 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 18:48:17 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'healthcare', 'iteration_index': 2, 'iteration_metadata': {'timestamp': 251862.799647291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': healthcare
2025-06-28 18:48:17 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 18:48:17 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 18:48:17 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 18:48:17 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 18:48:17 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 18:48:17 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 18:48:17 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'healthcare', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 18:48:17 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'healthcare', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 18:48:17 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'healthcare', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 18:48:17 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:17 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 11, 'workflow_status': 'running'}
2025-06-28 18:48:17 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 15f307b4-596a-43f1-96fd-87f8b59e8206) using provided producer.
2025-06-28 18:48:17 - NodeExecutor - DEBUG - Added correlation_id 0dece4c1-d537-423d-89d2-30fe493c7758 to payload
2025-06-28 18:48:17 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 18:48:17 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'healthcare', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '15f307b4-596a-43f1-96fd-87f8b59e8206', 'correlation_id': '0dece4c1-d537-423d-89d2-30fe493c7758', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 18:48:17 - NodeExecutor - DEBUG - Request 15f307b4-596a-43f1-96fd-87f8b59e8206 sent successfully using provided producer.
2025-06-28 18:48:17 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 15f307b4-596a-43f1-96fd-87f8b59e8206...
2025-06-28 18:48:18 - NodeExecutor - DEBUG - Result consumer received message: Offset=859
2025-06-28 18:48:18 - NodeExecutor - DEBUG - Received valid result for request_id 15f307b4-596a-43f1-96fd-87f8b59e8206
2025-06-28 18:48:18 - NodeExecutor - INFO - Result received for request 15f307b4-596a-43f1-96fd-87f8b59e8206.
2025-06-28 18:48:18 - TransitionHandler - INFO - Execution result from Components executor: "healthcare and AI"
2025-06-28 18:48:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'healthcare and AI', 'status': 'completed', 'sequence': 12, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 18:48:18 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'healthcare and AI'}, 'status': 'completed', 'timestamp': **********.2067158}}
2025-06-28 18:48:18 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 18:48:19 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 18:48:19 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:19 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 18:48:19 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'transition-LoopNode-*************', 'current_iteration'}
2025-06-28 18:48:19 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 18:48:19 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 18:48:19 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 18:48:19 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 18:48:19 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 18:48:19 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 18:48:19 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 18:48:19 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 18:48:19 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 18:48:19 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.30 seconds
2025-06-28 18:48:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'result': 'Completed transition in 2.30 seconds', 'message': 'Transition completed in 2.30 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 13, 'workflow_status': 'running'}
2025-06-28 18:48:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'status': 'loop_completed', 'metadata': {'success': True, 'final_results': ['blockchain and AI', 'automation and AI', 'healthcare and AI'], 'iteration_count': 3, 'total_iterations': 3}, 'transition_id': 'transition-LoopNode-*************', 'sequence': 14, 'workflow_status': 'running'}
2025-06-28 18:48:19 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-28 18:48:19 - TransitionHandler - INFO - Execution result from loop executor: {
  "final_results": [
    "blockchain and AI",
    "automation and AI",
    "healthcare and AI"
  ]
}
2025-06-28 18:48:19 - TransitionHandler - INFO - Checking execution result for errors: {
  "final_results": [
    "blockchain and AI",
    "automation and AI",
    "healthcare and AI"
  ]
}
2025-06-28 18:48:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition Result received.', 'result': {'final_results': ['blockchain and AI', 'automation and AI', 'healthcare and AI']}, 'status': 'completed', 'sequence': 15, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 18:48:19 - TransitionHandler - INFO - 🔄 Handling loop completion for transition: transition-LoopNode-*************
2025-06-28 18:48:19 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'final_results': ['blockchain and AI', 'automation and AI', 'healthcare and AI']}
2025-06-28 18:48:19 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 18:48:19 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 18:48:19 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:19 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 18:48:19 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'transition-LoopNode-*************', 'current_iteration'}
2025-06-28 18:48:19 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 18:48:19 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-28 18:48:19 - TransitionHandler - INFO - 🔍 Auto-detected loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-28 18:48:19 - TransitionHandler - DEBUG - 🔍 Loop completion filtering - Loop config: {'iteration_behavior': 'independent', 'iteration_source': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-06-28 18:48:19 - TransitionHandler - DEBUG - 🔍 Loop body transitions to filter: ['transition-CombineTextComponent-*************']
2025-06-28 18:48:19 - TransitionHandler - DEBUG - 🔍 Output data configs: [{'to_transition_id': 'transition-CombineTextComponent-*************', 'target_node_id': 'Combine Text', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'current_item', 'result_path': 'current_item', 'edge_id': 'reactflow__edge-LoopNode-*************current_item-CombineTextComponent-*************main_input'}]}}, {'to_transition_id': 'transition-MergeDataComponent-*************', 'target_node_id': 'Merge Data', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'final_results', 'result_path': 'final_results', 'edge_id': 'reactflow__edge-LoopNode-*************final_results-MergeDataComponent-*************main_input'}]}}]
2025-06-28 18:48:19 - TransitionHandler - DEBUG - 🚫 Skipping loop body transition 'transition-CombineTextComponent-*************' - should have been executed internally by loop executor
2025-06-28 18:48:19 - TransitionHandler - DEBUG - ✅ Adding exit transition 'transition-MergeDataComponent-*************' to next transitions
2025-06-28 18:48:19 - TransitionHandler - INFO - ✅ Loop completion handled for transition-LoopNode-*************. Next transitions: ['transition-MergeDataComponent-*************']
2025-06-28 18:48:19 - TransitionHandler - INFO - Completed transition transition-LoopNode-************* in 16.95 seconds
2025-06-28 18:48:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'result': 'Completed transition in 16.95 seconds', 'message': 'Transition completed in 16.95 seconds', 'transition_id': 'transition-LoopNode-*************', 'status': 'time_logged', 'sequence': 16, 'workflow_status': 'running'}
2025-06-28 18:48:19 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-MergeDataComponent-*************']]
2025-06-28 18:48:19 - EnhancedWorkflowEngine - INFO - Transition transition-LoopNode-************* completed successfully: 1 next transitions
2025-06-28 18:48:19 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-MergeDataComponent-*************']
2025-06-28 18:48:19 - EnhancedWorkflowEngine - INFO - Adding transition transition-MergeDataComponent-************* to pending (all dependencies met)
2025-06-28 18:48:19 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-06-28 18:48:20 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:0dece4c1-d537-423d-89d2-30fe493c7758'
2025-06-28 18:48:20 - RedisManager - DEBUG - Set key 'workflow_state:0dece4c1-d537-423d-89d2-30fe493c7758' with TTL of 600 seconds
2025-06-28 18:48:20 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 0dece4c1-d537-423d-89d2-30fe493c7758. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:20 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 18:48:20 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 18:48:20 - StateManager - INFO - Cleared 1 pending transitions: {'transition-MergeDataComponent-*************'}
2025-06-28 18:48:20 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 18:48:20 - StateManager - INFO - Terminated: False
2025-06-28 18:48:20 - StateManager - INFO - Pending transitions (0): []
2025-06-28 18:48:20 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 18:48:20 - StateManager - INFO - Completed transitions (6): ['current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************']
2025-06-28 18:48:20 - StateManager - INFO - Results stored for 6 transitions
2025-06-28 18:48:20 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 18:48:20 - StateManager - INFO - Workflow status: inactive
2025-06-28 18:48:20 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 18:48:20 - StateManager - INFO - Workflow status: inactive
2025-06-28 18:48:20 - StateManager - INFO - Workflow paused: False
2025-06-28 18:48:20 - StateManager - INFO - ==============================
2025-06-28 18:48:20 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-06-28 18:48:20 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 17, 'workflow_status': 'running'}
2025-06-28 18:48:20 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=standard, execution_type=Components)
2025-06-28 18:48:20 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 18:48:20 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-06-28 18:48:20 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 18:48:20 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 18:48:21 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 18:48:21 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-28 18:48:21 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': ['blockchain and AI', 'automation and AI', 'healthcare and AI']}
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-28 18:48:21 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 18:48:21 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': ['blockchain and AI', 'automation and AI', 'healthcare and AI']}
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_results → main_input via path 'final_results': ['blockchain and AI', 'automation and AI', 'healthcare and AI']
2025-06-28 18:48:21 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 18:48:21 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: []
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-06-28 18:48:21 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-06-28 18:48:21 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 2 fields (22 null/empty fields removed)
2025-06-28 18:48:21 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 18:48:21 - TransitionHandler - DEBUG - 📌 Added static parameter: merge_strategy = Aggregate
2025-06-28 18:48:21 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': ['blockchain and AI', 'automation and AI', 'healthcare and AI'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-28 18:48:21 - TransitionHandler - DEBUG - tool Parameters: {'main_input': ['blockchain and AI', 'automation and AI', 'healthcare and AI'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-28 18:48:21 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': ['blockchain and AI', 'automation and AI', 'healthcare and AI'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-28 18:48:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server MergeDataComponent', 'status': 'connecting', 'sequence': 18, 'workflow_status': 'running'}
2025-06-28 18:48:21 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: 9a4c6716-3931-4a50-a680-709938a469e7) using provided producer.
2025-06-28 18:48:21 - NodeExecutor - DEBUG - Added correlation_id 0dece4c1-d537-423d-89d2-30fe493c7758 to payload
2025-06-28 18:48:21 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-06-28 18:48:21 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': ['blockchain and AI', 'automation and AI', 'healthcare and AI'], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}, 'request_id': '9a4c6716-3931-4a50-a680-709938a469e7', 'correlation_id': '0dece4c1-d537-423d-89d2-30fe493c7758', 'transition_id': 'transition-MergeDataComponent-*************'}
2025-06-28 18:48:21 - NodeExecutor - DEBUG - Request 9a4c6716-3931-4a50-a680-709938a469e7 sent successfully using provided producer.
2025-06-28 18:48:21 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 9a4c6716-3931-4a50-a680-709938a469e7...
2025-06-28 18:48:22 - NodeExecutor - DEBUG - Result consumer received message: Offset=860
2025-06-28 18:48:22 - NodeExecutor - DEBUG - Received valid result for request_id 9a4c6716-3931-4a50-a680-709938a469e7
2025-06-28 18:48:22 - NodeExecutor - INFO - Result received for request 9a4c6716-3931-4a50-a680-709938a469e7.
2025-06-28 18:48:22 - TransitionHandler - INFO - Execution result from Components executor: [
  "blockchain and AI",
  "automation and AI",
  "healthcare and AI"
]
2025-06-28 18:48:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': ['blockchain and AI', 'automation and AI', 'healthcare and AI'], 'status': 'completed', 'sequence': 19, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 18:48:22 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': ['blockchain and AI', 'automation and AI', 'healthcare and AI']}, 'status': 'completed', 'timestamp': **********.149266}}
2025-06-28 18:48:22 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-06-28 18:48:22 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-06-28 18:48:22 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:48:22 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 18:48:22 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-MergeDataComponent-*************', 'transition-CombineTextComponent-*************', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'transition-LoopNode-*************', 'current_iteration'}
2025-06-28 18:48:22 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-06-28 18:48:22 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'list'>
2025-06-28 18:48:22 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 18:48:22 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'list'>
2025-06-28 18:48:22 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-06-28 18:48:22 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 18:48:22 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 18:48:22 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-06-28 18:48:22 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 2.19 seconds
2025-06-28 18:48:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 20, corr_id 0dece4c1-d537-423d-89d2-30fe493c7758):
2025-06-28 18:48:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'result': 'Completed transition in 2.19 seconds', 'message': 'Transition completed in 2.19 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 20, 'workflow_status': 'running'}
2025-06-28 18:48:22 - EnhancedWorkflowEngine - DEBUG - Results: [[]]
2025-06-28 18:48:22 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 18:48:22 - EnhancedWorkflowEngine - INFO - Workflow is marked as terminated, ending workflow execution.
2025-06-28 18:48:22 - StateManager - DEBUG - Workflow active: False (terminated=True, pending=empty, waiting=empty)
2025-06-28 18:48:22 - EnhancedWorkflowEngine - INFO - Workflow execution completed.
2025-06-28 18:48:22 - KafkaWorkflowConsumer - INFO - Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' final status: completed, result: Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' executed successfully.
2025-06-28 18:48:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dece4c1-d537-423d-89d2-30fe493c7758, response: {'status': 'complete', 'result': "Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' executed successfully.", 'workflow_status': 'completed'}
2025-06-28 18:48:22 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 0dece4c1-d537-423d-89d2-30fe493c7758 
2025-06-28 18:49:14 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:49:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:49:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:49:14 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 18:50:14 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:50:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:50:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:50:14 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 18:51:14 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:51:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:51:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:51:14 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 18:52:05 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a', 'data': b'expired'}
2025-06-28 18:52:05 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a'
2025-06-28 18:52:05 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a
2025-06-28 18:52:05 - RedisEventListener - DEBUG - Extracted key: workflow_state:ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a
2025-06-28 18:52:05 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 18:52:05 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 18:52:05 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a
2025-06-28 18:52:05 - RedisEventListener - INFO - Archiving workflow state for workflow: ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a
2025-06-28 18:52:09 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 18:52:09 - PostgresManager - DEBUG - Inserted new workflow state for correlation_id: 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 18:52:10 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 18:52:14 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:52:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:52:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:52:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 18:53:14 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:53:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:53:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:53:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 18:53:19 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-CombineTextComponent-*************', 'data': b'expired'}
2025-06-28 18:53:19 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-CombineTextComponent-*************'
2025-06-28 18:53:19 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-CombineTextComponent-*************
2025-06-28 18:53:19 - RedisEventListener - DEBUG - Extracted key: result:transition-CombineTextComponent-*************
2025-06-28 18:53:19 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 18:53:19 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 18:53:19 - RedisEventListener - INFO - Detected expired event for result of transition: transition-CombineTextComponent-*************
2025-06-28 18:53:19 - RedisEventListener - INFO - Archiving result for transition: transition-CombineTextComponent-*************
2025-06-28 18:53:19 - StateManager - DEBUG - Attempting to archive result for transition transition-CombineTextComponent-*************
2025-06-28 18:53:19 - StateManager - DEBUG - Provided result: False
2025-06-28 18:53:20 - StateManager - DEBUG - Trying to get result from Redis for transition transition-CombineTextComponent-*************
2025-06-28 18:53:20 - StateManager - DEBUG - No result found in Redis for transition transition-CombineTextComponent-*************
2025-06-28 18:53:20 - StateManager - DEBUG - Trying to get result from memory for transition transition-CombineTextComponent-*************
2025-06-28 18:53:20 - StateManager - DEBUG - Found result in memory for transition transition-CombineTextComponent-*************
2025-06-28 18:53:20 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-CombineTextComponent-*************
2025-06-28 18:53:20 - PostgresManager - DEBUG - Attempting to store transition result for transition-CombineTextComponent-************* in correlation 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 18:53:20 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-28 18:53:20 - PostgresManager - DEBUG - Result data: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'healthcare and AI'}, 'status': 'completed', 'timestamp': **********.2067158}}
2025-06-28 18:53:24 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 18:53:24 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-28 18:53:24 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-28 18:53:24 - PostgresManager - DEBUG - Inserting new record for transition transition-CombineTextComponent-*************
2025-06-28 18:53:24 - PostgresManager - DEBUG - Inserted new result for transition transition-CombineTextComponent-************* in correlation 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 18:53:25 - PostgresManager - DEBUG - Successfully stored transition result for transition-CombineTextComponent-*************
2025-06-28 18:53:25 - StateManager - INFO - Archived result for transition transition-CombineTextComponent-************* to PostgreSQL
2025-06-28 18:53:25 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-LoopNode-*************', 'data': b'expired'}
2025-06-28 18:53:25 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-LoopNode-*************'
2025-06-28 18:53:25 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-LoopNode-*************
2025-06-28 18:53:25 - RedisEventListener - DEBUG - Extracted key: result:transition-LoopNode-*************
2025-06-28 18:53:25 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 18:53:25 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 18:53:25 - RedisEventListener - INFO - Detected expired event for result of transition: transition-LoopNode-*************
2025-06-28 18:53:25 - RedisEventListener - INFO - Archiving result for transition: transition-LoopNode-*************
2025-06-28 18:53:25 - StateManager - DEBUG - Attempting to archive result for transition transition-LoopNode-*************
2025-06-28 18:53:25 - StateManager - DEBUG - Provided result: False
2025-06-28 18:53:26 - StateManager - DEBUG - Trying to get result from Redis for transition transition-LoopNode-*************
2025-06-28 18:53:26 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************
2025-06-28 18:53:26 - StateManager - DEBUG - Trying to get result from memory for transition transition-LoopNode-*************
2025-06-28 18:53:26 - StateManager - DEBUG - Found result in memory for transition transition-LoopNode-*************
2025-06-28 18:53:26 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-LoopNode-*************
2025-06-28 18:53:26 - PostgresManager - DEBUG - Attempting to store transition result for transition-LoopNode-************* in correlation 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 18:53:26 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-28 18:53:26 - PostgresManager - DEBUG - Result data: {'final_results': ['blockchain and AI', 'automation and AI', 'healthcare and AI']}
2025-06-28 18:53:29 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 18:53:30 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-28 18:53:30 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-28 18:53:30 - PostgresManager - DEBUG - Inserting new record for transition transition-LoopNode-*************
2025-06-28 18:53:30 - PostgresManager - DEBUG - Inserted new result for transition transition-LoopNode-************* in correlation 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 18:53:31 - PostgresManager - DEBUG - Successfully stored transition result for transition-LoopNode-*************
2025-06-28 18:53:31 - StateManager - INFO - Archived result for transition transition-LoopNode-************* to PostgreSQL
2025-06-28 18:53:31 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-MergeDataComponent-*************', 'data': b'expired'}
2025-06-28 18:53:31 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-MergeDataComponent-*************'
2025-06-28 18:53:31 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-MergeDataComponent-*************
2025-06-28 18:53:31 - RedisEventListener - DEBUG - Extracted key: result:transition-MergeDataComponent-*************
2025-06-28 18:53:31 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 18:53:31 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 18:53:31 - RedisEventListener - INFO - Detected expired event for result of transition: transition-MergeDataComponent-*************
2025-06-28 18:53:31 - RedisEventListener - INFO - Archiving result for transition: transition-MergeDataComponent-*************
2025-06-28 18:53:31 - StateManager - DEBUG - Attempting to archive result for transition transition-MergeDataComponent-*************
2025-06-28 18:53:31 - StateManager - DEBUG - Provided result: False
2025-06-28 18:53:32 - StateManager - DEBUG - Trying to get result from Redis for transition transition-MergeDataComponent-*************
2025-06-28 18:53:32 - StateManager - DEBUG - No result found in Redis for transition transition-MergeDataComponent-*************
2025-06-28 18:53:32 - StateManager - DEBUG - Trying to get result from memory for transition transition-MergeDataComponent-*************
2025-06-28 18:53:32 - StateManager - DEBUG - Found result in memory for transition transition-MergeDataComponent-*************
2025-06-28 18:53:32 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-MergeDataComponent-*************
2025-06-28 18:53:32 - PostgresManager - DEBUG - Attempting to store transition result for transition-MergeDataComponent-************* in correlation 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 18:53:32 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-28 18:53:32 - PostgresManager - DEBUG - Result data: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': ['blockchain and AI', 'automation and AI', 'healthcare and AI']}, 'status': 'completed', 'timestamp': **********.149266}}
2025-06-28 18:53:36 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 18:53:36 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-28 18:53:36 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-28 18:53:36 - PostgresManager - DEBUG - Inserting new record for transition transition-MergeDataComponent-*************
2025-06-28 18:53:36 - PostgresManager - DEBUG - Inserted new result for transition transition-MergeDataComponent-************* in correlation 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 18:53:37 - PostgresManager - DEBUG - Successfully stored transition result for transition-MergeDataComponent-*************
2025-06-28 18:53:37 - StateManager - INFO - Archived result for transition transition-MergeDataComponent-************* to PostgreSQL
2025-06-28 18:54:14 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:54:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:54:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:54:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 18:55:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:55:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 18:55:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              2025-06-28 19:01:14 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 19:01:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:01:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:01:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 19:02:14 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 19:02:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:02:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:02:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 19:03:04 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:loop_iteration_0', 'data': b'expired'}
2025-06-28 19:03:04 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:loop_iteration_0'
2025-06-28 19:03:04 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:loop_iteration_0
2025-06-28 19:03:04 - RedisEventListener - DEBUG - Extracted key: result:loop_iteration_0
2025-06-28 19:03:04 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 19:03:04 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 19:03:04 - RedisEventListener - INFO - Detected expired event for result of transition: loop_iteration_0
2025-06-28 19:03:04 - RedisEventListener - INFO - Archiving result for transition: loop_iteration_0
2025-06-28 19:03:04 - StateManager - DEBUG - Attempting to archive result for transition loop_iteration_0
2025-06-28 19:03:05 - StateManager - DEBUG - Provided result: False
2025-06-28 19:03:05 - StateManager - DEBUG - Trying to get result from Redis for transition loop_iteration_0
2025-06-28 19:03:06 - StateManager - DEBUG - No result found in Redis for transition loop_iteration_0
2025-06-28 19:03:06 - StateManager - DEBUG - Trying to get result from memory for transition loop_iteration_0
2025-06-28 19:03:06 - StateManager - DEBUG - Found result in memory for transition loop_iteration_0
2025-06-28 19:03:06 - StateManager - DEBUG - Archiving result to PostgreSQL for transition loop_iteration_0
2025-06-28 19:03:06 - PostgresManager - DEBUG - Attempting to store transition result for loop_iteration_0 in correlation 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 19:03:06 - PostgresManager - DEBUG - Result data type: <class 'str'>
2025-06-28 19:03:06 - PostgresManager - DEBUG - Result data: blockchain
2025-06-28 19:03:09 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 19:03:10 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-28 19:03:10 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-28 19:03:10 - PostgresManager - DEBUG - Inserting new record for transition loop_iteration_0
2025-06-28 19:03:10 - PostgresManager - DEBUG - Inserted new result for transition loop_iteration_0 in correlation 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 19:03:10 - PostgresManager - DEBUG - Successfully stored transition result for loop_iteration_0
2025-06-28 19:03:10 - StateManager - INFO - Archived result for transition loop_iteration_0 to PostgreSQL
2025-06-28 19:03:10 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:loop_iteration_1', 'data': b'expired'}
2025-06-28 19:03:10 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:loop_iteration_1'
2025-06-28 19:03:10 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:loop_iteration_1
2025-06-28 19:03:10 - RedisEventListener - DEBUG - Extracted key: result:loop_iteration_1
2025-06-28 19:03:10 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 19:03:10 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 19:03:10 - RedisEventListener - INFO - Detected expired event for result of transition: loop_iteration_1
2025-06-28 19:03:10 - RedisEventListener - INFO - Archiving result for transition: loop_iteration_1
2025-06-28 19:03:10 - StateManager - DEBUG - Attempting to archive result for transition loop_iteration_1
2025-06-28 19:03:11 - StateManager - DEBUG - Provided result: False
2025-06-28 19:03:11 - StateManager - DEBUG - Trying to get result from Redis for transition loop_iteration_1
2025-06-28 19:03:12 - StateManager - DEBUG - No result found in Redis for transition loop_iteration_1
2025-06-28 19:03:12 - StateManager - DEBUG - Trying to get result from memory for transition loop_iteration_1
2025-06-28 19:03:12 - StateManager - DEBUG - Found result in memory for transition loop_iteration_1
2025-06-28 19:03:12 - StateManager - DEBUG - Archiving result to PostgreSQL for transition loop_iteration_1
2025-06-28 19:03:12 - PostgresManager - DEBUG - Attempting to store transition result for loop_iteration_1 in correlation 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 19:03:12 - PostgresManager - DEBUG - Result data type: <class 'str'>
2025-06-28 19:03:12 - PostgresManager - DEBUG - Result data: automation
2025-06-28 19:03:15 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 19:03:15 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-28 19:03:15 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-28 19:03:15 - PostgresManager - DEBUG - Inserting new record for transition loop_iteration_1
2025-06-28 19:03:16 - PostgresManager - DEBUG - Inserted new result for transition loop_iteration_1 in correlation 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 19:03:16 - PostgresManager - DEBUG - Successfully stored transition result for loop_iteration_1
2025-06-28 19:03:16 - StateManager - INFO - Archived result for transition loop_iteration_1 to PostgreSQL
2025-06-28 19:03:16 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 19:03:16 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pmessage', b'__keyspace@5__:*', b'__keyspace@5__:result:loop_iteration_2', b'expired']
2025-06-28 19:03:18 - RedisEventListener - WARNING - Failed to send keep-alive PING to Redis: Bad response from PING health check
2025-06-28 19:03:18 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=False
2025-06-28 19:03:18 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:current_iteration', 'data': b'expired'}
2025-06-28 19:03:18 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:current_iteration'
2025-06-28 19:03:18 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:current_iteration
2025-06-28 19:03:18 - RedisEventListener - DEBUG - Extracted key: result:current_iteration
2025-06-28 19:03:18 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-28 19:03:18 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-28 19:03:18 - RedisEventListener - INFO - Detected expired event for result of transition: current_iteration
2025-06-28 19:03:18 - RedisEventListener - INFO - Archiving result for transition: current_iteration
2025-06-28 19:03:18 - StateManager - DEBUG - Attempting to archive result for transition current_iteration
2025-06-28 19:03:19 - StateManager - DEBUG - Provided result: False
2025-06-28 19:03:19 - StateManager - DEBUG - Trying to get result from Redis for transition current_iteration
2025-06-28 19:03:19 - StateManager - DEBUG - No result found in Redis for transition current_iteration
2025-06-28 19:03:19 - StateManager - DEBUG - Trying to get result from memory for transition current_iteration
2025-06-28 19:03:19 - StateManager - DEBUG - Found result in memory for transition current_iteration
2025-06-28 19:03:19 - StateManager - DEBUG - Archiving result to PostgreSQL for transition current_iteration
2025-06-28 19:03:19 - PostgresManager - DEBUG - Attempting to store transition result for current_iteration in correlation 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 19:03:19 - PostgresManager - DEBUG - Result data type: <class 'str'>
2025-06-28 19:03:19 - PostgresManager - DEBUG - Result data: healthcare
2025-06-28 19:03:23 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 19:03:23 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-28 19:03:23 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-28 19:03:23 - PostgresManager - DEBUG - Inserting new record for transition current_iteration
2025-06-28 19:03:24 - PostgresManager - DEBUG - Inserted new result for transition current_iteration in correlation 0dece4c1-d537-423d-89d2-30fe493c7758
2025-06-28 19:03:24 - PostgresManager - DEBUG - Successfully stored transition result for current_iteration
2025-06-28 19:03:24 - StateManager - INFO - Archived result for transition current_iteration to PostgreSQL
2025-06-28 19:04:16 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 19:04:16 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:04:17 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:04:17 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 19:05:16 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 19:05:16 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:05:17 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:05:17 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 19:06:16 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 19:06:16 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:06:17 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:06:17 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 19:07:16 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 19:07:16 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:07:17 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:07:17 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 19:08:16 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 19:08:16 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:08:17 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:08:17 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 19:09:16 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 19:09:16 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:09:17 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:09:17 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 19:10:16 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 19:10:16 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:10:17 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:10:17 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 19:11:16 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 19:11:16 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:11:17 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:11:17 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 19:12:16 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 19:12:16 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:12:17 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:12:17 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 19:13:16 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 19:13:16 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:13:17 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-06-28 19:13:17 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
