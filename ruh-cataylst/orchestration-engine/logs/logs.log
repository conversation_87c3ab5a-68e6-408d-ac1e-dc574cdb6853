2025-06-28 23:03:27 - Main - INFO - Starting Server
2025-06-28 23:03:27 - Main - INFO - Connection at: **************:9092
2025-06-28 23:03:27 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 23:03:27 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-28 23:03:27 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 23:03:27 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 23:03:27 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 23:03:29 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 23:03:29 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 23:03:30 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 23:03:32 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-28 23:03:32 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-28 23:03:35 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 23:03:35 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-28 23:03:35 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 23:03:37 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 23:03:37 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 23:03:39 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 23:03:39 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-28 23:03:39 - RedisEventListener - INFO - Redis event listener started
2025-06-28 23:03:39 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-28 23:03:39 - StateManager - DEBUG - Using provided database connections
2025-06-28 23:03:39 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 23:03:39 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 23:03:39 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 23:03:39 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-28 23:03:39 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 23:03:39 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 23:03:39 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-28 23:03:40 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-28 23:03:40 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-28 23:03:40 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-28 23:03:42 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-28 23:03:42 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-28 23:03:42 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-28 23:03:47 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-28 23:03:53 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-28 23:03:53 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-28 23:03:53 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-28 23:04:00 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-28 23:04:00 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-28 23:04:00 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-28 23:04:00 - NodeExecutor - DEBUG - Result consumer received message: Offset=886
2025-06-28 23:04:00 - NodeExecutor - WARNING - Received result for unknown or timed-out request_id: 5ab9d979-3585-486a-83e9-0aa74affc19f
2025-06-28 23:04:06 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-28 23:04:06 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-28 23:04:06 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1027
2025-06-28 23:04:06 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': **********, 'task_type': 'workflow', 'data': {'workflow_id': 'feda07bf-a91e-4004-80cb-72416cdb5a43', 'payload': {'user_dependent_fields': ['iteration_list'], 'user_payload_template': {'iteration_list': {'value': '["blockchain","automation","healthcare"]', 'transition_id': 'LoopNode-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-06-28 23:04:06 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 23:04:06 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 23:04:07 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-28 23:04:07 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow LOOP TEST DO NOT DELETE retrieved successfully",
  "workflow": {
    "id": "feda07bf-a91e-4004-80cb-72416cdb5a43",
    "name": "LOOP TEST DO NOT DELETE",
    "description": "LOOP_TEST_DO_NOT_DELETE",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/16a976d6-6669-4cb4-8207-4f9b8626a0b8.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/91cb74a1-1bfd-42fa-b59d-58773b35d33a.json",
    "start_nodes": [
      {
        "field": "iteration_list",
        "type": "list",
        "transition_id": "transition-LoopNode-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-24T11:13:46.129953",
    "updated_at": "2025-06-28T14:13:59.013729",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "LoopNode",
        "display_name": "For Each Loop",
        "type": "component",
        "transition_id": "transition-LoopNode-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1751052569332"
      },
      {
        "name": "MCP_script-generation-mcp_script_generate",
        "id": "748a8221-d7d9-4352-93ae-00700f4d28b1",
        "transition_id": "transition-MCP_script-generation-mcp_script_generate-*************",
        "type": "mcp",
        "display_name": "script-generation-mcp - script_generate",
        "data": {
          "input_schema": {
            "$defs": {
              "Keywords": {
                "properties": {
                  "time": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Time"
                  },
                  "objective": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Objective"
                  },
                  "audience": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Audience"
                  },
                  "gender": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Gender"
                  },
                  "tone": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Tone"
                  },
                  "speakers": {
                    "anyOf": [
                      {
                        "items": {
                          "type": "string"
                        },
                        "type": "array"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Speakers"
                  }
                },
                "title": "Keywords",
                "type": "object"
              },
              "ScriptType": {
                "enum": [
                  "VIDEO",
                  "TOPIC",
                  "SCRIPT",
                  "BLOG",
                  "AI"
                ],
                "title": "ScriptType",
                "type": "string"
              },
              "VideoType": {
                "enum": [
                  "SHORT",
                  "LONG"
                ],
                "title": "VideoType",
                "type": "string"
              }
            },
            "properties": {
              "topic": {
                "title": "Topic",
                "type": "string"
              },
              "script_type": {
                "$ref": "#/$defs/ScriptType",
                "default": "TOPIC"
              },
              "keywords": {
                "$ref": "#/$defs/Keywords"
              },
              "video_type": {
                "$ref": "#/$defs/VideoType",
                "default": "SHORT"
              },
              "link": {
                "anyOf": [
                  {
                    "format": "uri",
                    "maxLength": 2083,
                    "minLength": 1,
                    "type": "string"
                  },
                  {
                    "type": "null"
                  }
                ],
                "default": null,
                "title": "Link"
              }
            },
            "required": [
              "topic"
            ],
            "title": "GenerateScriptInput",
            "type": "object"
          },
          "output_schema": {
            "properties": {
              "title": {
                "type": "string",
                "description": "Title of the generated script",
                "title": "title"
              },
              "script": {
                "type": "string",
                "description": "The generated script",
                "title": "script"
              },
              "script_type": {
                "type": "string",
                "description": "Type of the script",
                "title": "script_type"
              },
              "video_type": {
                "type": "string",
                "description": "The type of video",
                "title": "video_type"
              },
              "link": {
                "type": "string",
                "format": "uri",
                "description": "Optional link for the script",
                "title": "link"
              }
            }
          }
        }
      }
    ],
    "is_updated": true
  }
}
2025-06-28 23:04:07 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for feda07bf-a91e-4004-80cb-72416cdb5a43 - server_script_path is optional
2025-06-28 23:04:07 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-28 23:04:07 - StateManager - DEBUG - Using global database connections from initializer
2025-06-28 23:04:07 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 23:04:07 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 23:04:07 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 23:04:07 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 23:04:07 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 23:04:08 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-28 23:04:08 - StateManager - DEBUG - Using provided database connections
2025-06-28 23:04:08 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 23:04:08 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 23:04:08 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 23:04:08 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 23:04:08 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 23:04:08 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-28 23:04:08 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751052569332: ['transition-LoopNode-*************']
2025-06-28 23:04:08 - StateManager - INFO - Built dependency map for 3 transitions
2025-06-28 23:04:08 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-28 23:04:08 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751052569332 depends on: ['transition-LoopNode-*************']
2025-06-28 23:04:08 - MCPToolExecutor - DEBUG - Set correlation ID to: 07aef36a-5366-4757-a0ef-2c34a30e5991
2025-06-28 23:04:08 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 07aef36a-5366-4757-a0ef-2c34a30e5991 in tool_executor
2025-06-28 23:04:08 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 23:04:08 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-06-28 23:04:08 - NodeExecutor - DEBUG - Set correlation ID to: 07aef36a-5366-4757-a0ef-2c34a30e5991
2025-06-28 23:04:08 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 07aef36a-5366-4757-a0ef-2c34a30e5991 in node_executor
2025-06-28 23:04:08 - AgentExecutor - DEBUG - Set correlation ID to: 07aef36a-5366-4757-a0ef-2c34a30e5991
2025-06-28 23:04:08 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 07aef36a-5366-4757-a0ef-2c34a30e5991 in agent_executor
2025-06-28 23:04:08 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 23:04:08 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-06-28 23:04:08 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-28 23:04:08 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 07aef36a-5366-4757-a0ef-2c34a30e5991
2025-06-28 23:04:08 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 07aef36a-5366-4757-a0ef-2c34a30e5991
2025-06-28 23:04:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-28 23:04:08 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 23:04:08 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 23:04:08 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 23:04:08 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 23:04:09 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:07aef36a-5366-4757-a0ef-2c34a30e5991'
2025-06-28 23:04:09 - RedisManager - DEBUG - Set key 'workflow_state:07aef36a-5366-4757-a0ef-2c34a30e5991' with TTL of 600 seconds
2025-06-28 23:04:09 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 07aef36a-5366-4757-a0ef-2c34a30e5991. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:09 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 23:04:09 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 23:04:09 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 23:04:09 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 23:04:09 - StateManager - INFO - Terminated: False
2025-06-28 23:04:09 - StateManager - INFO - Pending transitions (0): []
2025-06-28 23:04:09 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 23:04:09 - StateManager - INFO - Completed transitions (0): []
2025-06-28 23:04:09 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 23:04:09 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:04:09 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:04:09 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:04:09 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:04:09 - StateManager - INFO - Workflow paused: False
2025-06-28 23:04:09 - StateManager - INFO - ==============================
2025-06-28 23:04:09 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 23:04:09 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:09 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-28 23:04:09 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 23:04:09 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-28 23:04:09 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 23:04:09 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 23:04:09 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 23:04:09 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 23:04:09 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 23:04:09 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 23:04:09 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:04:09 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:04:09 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:09 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-28 23:04:09 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 23:04:09 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 23:04:09 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 23:04:09 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 23:04:09 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 23:04:09 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 23:04:09 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-28 23:04:09 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-28 23:04:09 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-28 23:04:09 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-28 23:04:09 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-28 23:04:09 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:04:09 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-28 23:04:09 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-28 23:04:09 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-28 23:04:09 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-28 23:04:09 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-28 23:04:09 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-28 23:04:09 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-28 23:04:09 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-28 23:04:09 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:09 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-28 23:04:09 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain
2025-06-28 23:04:09 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 23:04:10 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 23:04:10 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 23:04:10 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:10 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:10 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 23:04:10 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 23:04:11 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 23:04:11 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 23:04:11 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:11 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:11 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0'}
2025-06-28 23:04:11 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266750.62299325, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:12 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 23:04:12 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 23:04:12 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:12 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:12 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 23:04:12 - LoopExecutor - DEBUG - 💾 Stored loop iteration data under transition ID: transition-LoopNode-*************
2025-06-28 23:04:12 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-28 23:04:12 - LoopExecutor - DEBUG - 🚀 Starting entry transition: transition-CombineTextComponent-*************
2025-06-28 23:04:12 - LoopExecutor - DEBUG - 🎯 Executing transition transition-CombineTextComponent-************* through orchestration engine
2025-06-28 23:04:12 - LoopExecutor - INFO - 🚀 REAL EXECUTION: Starting transition-CombineTextComponent-************* via transition handler
2025-06-28 23:04:12 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 23:04:12 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:12 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 2, 'workflow_status': 'running'}
2025-06-28 23:04:12 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 23:04:12 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 23:04:12 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 23:04:12 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 23:04:12 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 23:04:13 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 23:04:13 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 23:04:13 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266750.62299325, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:04:13 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 23:04:13 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266750.62299325, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-28 23:04:13 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 23:04:13 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 23:04:13 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 23:04:13 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 23:04:13 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 23:04:13 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 23:04:13 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:13 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:13 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:13 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:13 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 3, 'workflow_status': 'running'}
2025-06-28 23:04:13 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 61f334e6-228b-43d6-8739-10eea18df9fb) using provided producer.
2025-06-28 23:04:13 - NodeExecutor - DEBUG - Added correlation_id 07aef36a-5366-4757-a0ef-2c34a30e5991 to payload
2025-06-28 23:04:13 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 23:04:13 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '61f334e6-228b-43d6-8739-10eea18df9fb', 'correlation_id': '07aef36a-5366-4757-a0ef-2c34a30e5991', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 23:04:13 - NodeExecutor - DEBUG - Request 61f334e6-228b-43d6-8739-10eea18df9fb sent successfully using provided producer.
2025-06-28 23:04:13 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 61f334e6-228b-43d6-8739-10eea18df9fb...
2025-06-28 23:04:13 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1027, corr_id: 07aef36a-5366-4757-a0ef-2c34a30e5991
2025-06-28 23:04:14 - NodeExecutor - DEBUG - Result consumer received message: Offset=887
2025-06-28 23:04:14 - NodeExecutor - DEBUG - Received valid result for request_id 61f334e6-228b-43d6-8739-10eea18df9fb
2025-06-28 23:04:14 - NodeExecutor - INFO - Result received for request 61f334e6-228b-43d6-8739-10eea18df9fb.
2025-06-28 23:04:14 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-28 23:04:14 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:14 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 4, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 23:04:14 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751132054.368866}}
2025-06-28 23:04:14 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 23:04:15 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 23:04:15 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:15 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:15 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 23:04:15 - LoopExecutor - INFO - 🚨 async notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-28 23:04:15 - LoopExecutor - DEBUG - 🔔 Received completion notification for transition: transition-CombineTextComponent-*************
2025-06-28 23:04:15 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:15 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.68 seconds
2025-06-28 23:04:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Completed transition in 2.68 seconds', 'message': 'Transition completed in 2.68 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 5, 'workflow_status': 'running'}
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-28 23:04:15 - LoopExecutor - DEBUG - 🔗 Adding next transitions to orchestration engine queue: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:15 - StateManager - INFO - Added transitions to pending: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:15 - StateManager - DEBUG - Updated pending transitions: {'transition-MCP_script-generation-mcp_script_generate-*************'}
2025-06-28 23:04:15 - LoopExecutor - DEBUG - 🚀 Creating new orchestration engine execution task to process transitions
2025-06-28 23:04:15 - LoopExecutor - DEBUG - ✅ Started transition chain from transition-CombineTextComponent-*************
2025-06-28 23:04:15 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:15 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:15 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 23:04:15 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 23:04:15 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 23:04:15 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 23:04:15 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:07aef36a-5366-4757-a0ef-2c34a30e5991'
2025-06-28 23:04:15 - RedisManager - DEBUG - Set key 'workflow_state:07aef36a-5366-4757-a0ef-2c34a30e5991' with TTL of 600 seconds
2025-06-28 23:04:15 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 07aef36a-5366-4757-a0ef-2c34a30e5991. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:15 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 23:04:15 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 23:04:15 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 23:04:15 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 23:04:15 - StateManager - INFO - Terminated: False
2025-06-28 23:04:15 - StateManager - INFO - Pending transitions (0): []
2025-06-28 23:04:15 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 23:04:15 - StateManager - INFO - Completed transitions (0): []
2025-06-28 23:04:15 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 23:04:15 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:04:15 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:04:15 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:04:15 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:04:15 - StateManager - INFO - Workflow paused: False
2025-06-28 23:04:15 - StateManager - INFO - ==============================
2025-06-28 23:04:15 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 23:04:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-06-28 23:04:15 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 23:04:15 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 23:04:15 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 23:04:15 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:04:15 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:04:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 7, 'workflow_status': 'running'}
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 23:04:15 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 23:04:15 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 23:04:15 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-28 23:04:15 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-28 23:04:15 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-28 23:04:15 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-28 23:04:15 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-28 23:04:15 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:04:15 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-28 23:04:15 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-28 23:04:15 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-28 23:04:15 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-28 23:04:15 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-28 23:04:15 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-28 23:04:15 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-28 23:04:15 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-28 23:04:15 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:15 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-28 23:04:15 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain
2025-06-28 23:04:15 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 23:04:16 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 23:04:16 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 23:04:16 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:16 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:16 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 23:04:16 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 23:04:16 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 23:04:17 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 23:04:17 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:17 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:17 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0'}
2025-06-28 23:04:17 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266756.424157, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:17 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 23:04:18 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 23:04:18 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:18 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:18 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 23:04:18 - LoopExecutor - DEBUG - 💾 Stored loop iteration data under transition ID: transition-LoopNode-*************
2025-06-28 23:04:18 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-28 23:04:18 - LoopExecutor - DEBUG - 🚀 Starting entry transition: transition-CombineTextComponent-*************
2025-06-28 23:04:18 - LoopExecutor - DEBUG - 🎯 Executing transition transition-CombineTextComponent-************* through orchestration engine
2025-06-28 23:04:18 - LoopExecutor - INFO - 🚀 REAL EXECUTION: Starting transition-CombineTextComponent-************* via transition handler
2025-06-28 23:04:18 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 23:04:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 8, 'workflow_status': 'running'}
2025-06-28 23:04:18 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 23:04:18 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 23:04:18 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 23:04:18 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 23:04:18 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 23:04:19 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 23:04:19 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 23:04:19 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266756.424157, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:04:19 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 23:04:19 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266756.424157, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-28 23:04:19 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 23:04:19 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 23:04:19 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 23:04:19 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 23:04:19 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 23:04:19 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 23:04:19 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:19 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:19 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 9, 'workflow_status': 'running'}
2025-06-28 23:04:19 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 327f9b57-287a-41ef-b586-c16bc2afe025) using provided producer.
2025-06-28 23:04:19 - NodeExecutor - DEBUG - Added correlation_id 07aef36a-5366-4757-a0ef-2c34a30e5991 to payload
2025-06-28 23:04:19 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 23:04:19 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '327f9b57-287a-41ef-b586-c16bc2afe025', 'correlation_id': '07aef36a-5366-4757-a0ef-2c34a30e5991', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 23:04:19 - NodeExecutor - DEBUG - Request 327f9b57-287a-41ef-b586-c16bc2afe025 sent successfully using provided producer.
2025-06-28 23:04:19 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 327f9b57-287a-41ef-b586-c16bc2afe025...
2025-06-28 23:04:20 - NodeExecutor - DEBUG - Result consumer received message: Offset=888
2025-06-28 23:04:20 - NodeExecutor - DEBUG - Received valid result for request_id 327f9b57-287a-41ef-b586-c16bc2afe025
2025-06-28 23:04:20 - NodeExecutor - INFO - Result received for request 327f9b57-287a-41ef-b586-c16bc2afe025.
2025-06-28 23:04:20 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-28 23:04:20 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 10, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 23:04:20 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751132060.0501409}}
2025-06-28 23:04:20 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 23:04:20 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 23:04:20 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:20 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:20 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 23:04:20 - LoopExecutor - INFO - 🚨 async notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-28 23:04:20 - LoopExecutor - DEBUG - 🔔 Received completion notification for transition: transition-CombineTextComponent-*************
2025-06-28 23:04:20 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:20 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.64 seconds
2025-06-28 23:04:20 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Completed transition in 2.64 seconds', 'message': 'Transition completed in 2.64 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 11, 'workflow_status': 'running'}
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:20 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-28 23:04:20 - LoopExecutor - DEBUG - 🔗 Adding next transitions to orchestration engine queue: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:20 - StateManager - INFO - Added transitions to pending: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:20 - StateManager - DEBUG - Updated pending transitions: {'transition-MCP_script-generation-mcp_script_generate-*************'}
2025-06-28 23:04:20 - LoopExecutor - DEBUG - 🚀 Creating new orchestration engine execution task to process transitions
2025-06-28 23:04:20 - LoopExecutor - DEBUG - ✅ Started transition chain from transition-CombineTextComponent-*************
2025-06-28 23:04:20 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:20 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:20 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 23:04:20 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 23:04:20 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 23:04:20 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 23:04:21 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:07aef36a-5366-4757-a0ef-2c34a30e5991'
2025-06-28 23:04:21 - RedisManager - DEBUG - Set key 'workflow_state:07aef36a-5366-4757-a0ef-2c34a30e5991' with TTL of 600 seconds
2025-06-28 23:04:21 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 07aef36a-5366-4757-a0ef-2c34a30e5991. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:21 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 23:04:21 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 23:04:21 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 23:04:21 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 23:04:21 - StateManager - INFO - Terminated: False
2025-06-28 23:04:21 - StateManager - INFO - Pending transitions (0): []
2025-06-28 23:04:21 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 23:04:21 - StateManager - INFO - Completed transitions (0): []
2025-06-28 23:04:21 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 23:04:21 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:04:21 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:04:21 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:04:21 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:04:21 - StateManager - INFO - Workflow paused: False
2025-06-28 23:04:21 - StateManager - INFO - ==============================
2025-06-28 23:04:21 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 23:04:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 12, 'workflow_status': 'running'}
2025-06-28 23:04:21 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 23:04:21 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-28 23:04:21 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 23:04:21 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 23:04:21 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 23:04:21 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 23:04:21 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 23:04:21 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 23:04:21 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:04:21 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:04:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 13, 'workflow_status': 'running'}
2025-06-28 23:04:21 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 23:04:21 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 23:04:21 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 23:04:21 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 23:04:21 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 23:04:21 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 23:04:21 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-28 23:04:21 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-28 23:04:21 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-28 23:04:21 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-28 23:04:21 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-28 23:04:21 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:04:21 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-28 23:04:21 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-28 23:04:21 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-28 23:04:21 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-28 23:04:21 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-28 23:04:21 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-28 23:04:21 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-28 23:04:21 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-28 23:04:21 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:21 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-28 23:04:21 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain
2025-06-28 23:04:21 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 23:04:21 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 23:04:22 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 23:04:22 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:22 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:22 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 23:04:22 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 23:04:22 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 23:04:23 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 23:04:23 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:23 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:23 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0'}
2025-06-28 23:04:23 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266762.149831166, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:23 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 23:04:23 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 23:04:23 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:23 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:23 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 23:04:23 - LoopExecutor - DEBUG - 💾 Stored loop iteration data under transition ID: transition-LoopNode-*************
2025-06-28 23:04:23 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-28 23:04:23 - LoopExecutor - DEBUG - 🚀 Starting entry transition: transition-CombineTextComponent-*************
2025-06-28 23:04:23 - LoopExecutor - DEBUG - 🎯 Executing transition transition-CombineTextComponent-************* through orchestration engine
2025-06-28 23:04:23 - LoopExecutor - INFO - 🚀 REAL EXECUTION: Starting transition-CombineTextComponent-************* via transition handler
2025-06-28 23:04:23 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 23:04:23 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:23 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 14, 'workflow_status': 'running'}
2025-06-28 23:04:23 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 23:04:23 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 23:04:23 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 23:04:23 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 23:04:23 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 23:04:24 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 23:04:24 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 23:04:24 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266762.149831166, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:04:24 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 23:04:24 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266762.149831166, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-28 23:04:24 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 23:04:24 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 23:04:24 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 23:04:24 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 23:04:24 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 23:04:24 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 23:04:24 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:24 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:24 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:24 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 15, 'workflow_status': 'running'}
2025-06-28 23:04:24 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: f36968e7-e853-45a7-9ff5-cbb6f0b1feca) using provided producer.
2025-06-28 23:04:24 - NodeExecutor - DEBUG - Added correlation_id 07aef36a-5366-4757-a0ef-2c34a30e5991 to payload
2025-06-28 23:04:24 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 23:04:24 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': 'f36968e7-e853-45a7-9ff5-cbb6f0b1feca', 'correlation_id': '07aef36a-5366-4757-a0ef-2c34a30e5991', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 23:04:24 - NodeExecutor - DEBUG - Request f36968e7-e853-45a7-9ff5-cbb6f0b1feca sent successfully using provided producer.
2025-06-28 23:04:24 - NodeExecutor - DEBUG - Waiting indefinitely for result for request f36968e7-e853-45a7-9ff5-cbb6f0b1feca...
2025-06-28 23:04:25 - NodeExecutor - DEBUG - Result consumer received message: Offset=889
2025-06-28 23:04:25 - NodeExecutor - DEBUG - Received valid result for request_id f36968e7-e853-45a7-9ff5-cbb6f0b1feca
2025-06-28 23:04:25 - NodeExecutor - INFO - Result received for request f36968e7-e853-45a7-9ff5-cbb6f0b1feca.
2025-06-28 23:04:25 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-28 23:04:25 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:25 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 16, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 23:04:25 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751132065.7620401}}
2025-06-28 23:04:26 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 23:04:26 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 23:04:26 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:26 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:26 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 23:04:26 - LoopExecutor - INFO - 🚨 async notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-28 23:04:26 - LoopExecutor - DEBUG - 🔔 Received completion notification for transition: transition-CombineTextComponent-*************
2025-06-28 23:04:26 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:26 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.55 seconds
2025-06-28 23:04:26 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:26 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Completed transition in 2.55 seconds', 'message': 'Transition completed in 2.55 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 17, 'workflow_status': 'running'}
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:26 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-28 23:04:26 - LoopExecutor - DEBUG - 🔗 Adding next transitions to orchestration engine queue: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:26 - StateManager - INFO - Added transitions to pending: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:26 - StateManager - DEBUG - Updated pending transitions: {'transition-MCP_script-generation-mcp_script_generate-*************'}
2025-06-28 23:04:26 - LoopExecutor - DEBUG - 🚀 Creating new orchestration engine execution task to process transitions
2025-06-28 23:04:26 - LoopExecutor - DEBUG - ✅ Started transition chain from transition-CombineTextComponent-*************
2025-06-28 23:04:26 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:26 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:26 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 23:04:26 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 23:04:26 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 23:04:26 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 23:04:27 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:07aef36a-5366-4757-a0ef-2c34a30e5991'
2025-06-28 23:04:27 - RedisManager - DEBUG - Set key 'workflow_state:07aef36a-5366-4757-a0ef-2c34a30e5991' with TTL of 600 seconds
2025-06-28 23:04:27 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 07aef36a-5366-4757-a0ef-2c34a30e5991. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:27 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 23:04:27 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 23:04:27 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 23:04:27 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 23:04:27 - StateManager - INFO - Terminated: False
2025-06-28 23:04:27 - StateManager - INFO - Pending transitions (0): []
2025-06-28 23:04:27 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 23:04:27 - StateManager - INFO - Completed transitions (0): []
2025-06-28 23:04:27 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 23:04:27 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:04:27 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:04:27 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:04:27 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:04:27 - StateManager - INFO - Workflow paused: False
2025-06-28 23:04:27 - StateManager - INFO - ==============================
2025-06-28 23:04:27 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 23:04:27 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:27 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 18, 'workflow_status': 'running'}
2025-06-28 23:04:27 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 23:04:27 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-28 23:04:27 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 23:04:27 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 23:04:27 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 23:04:27 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 23:04:27 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 23:04:27 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 23:04:27 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:04:27 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:04:27 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:27 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 19, 'workflow_status': 'running'}
2025-06-28 23:04:27 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 23:04:27 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 23:04:27 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 23:04:27 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 23:04:27 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 23:04:27 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 23:04:27 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-28 23:04:27 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-28 23:04:27 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-28 23:04:27 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-28 23:04:27 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-28 23:04:27 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:04:27 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-28 23:04:27 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-28 23:04:27 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-28 23:04:27 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-28 23:04:27 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-28 23:04:27 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-28 23:04:27 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-28 23:04:27 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-28 23:04:27 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:27 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-28 23:04:27 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain
2025-06-28 23:04:27 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 23:04:27 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 23:04:28 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 23:04:28 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:28 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:28 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 23:04:28 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 23:04:28 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 23:04:28 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 23:04:28 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:28 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:28 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0'}
2025-06-28 23:04:28 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266767.846866833, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:29 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 23:04:29 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 23:04:29 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:29 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:29 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 23:04:29 - LoopExecutor - DEBUG - 💾 Stored loop iteration data under transition ID: transition-LoopNode-*************
2025-06-28 23:04:29 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-28 23:04:29 - LoopExecutor - DEBUG - 🚀 Starting entry transition: transition-CombineTextComponent-*************
2025-06-28 23:04:29 - LoopExecutor - DEBUG - 🎯 Executing transition transition-CombineTextComponent-************* through orchestration engine
2025-06-28 23:04:29 - LoopExecutor - INFO - 🚀 REAL EXECUTION: Starting transition-CombineTextComponent-************* via transition handler
2025-06-28 23:04:29 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 23:04:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 20, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 20, 'workflow_status': 'running'}
2025-06-28 23:04:29 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 23:04:29 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 23:04:29 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 23:04:29 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 23:04:29 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 23:04:30 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 23:04:30 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 23:04:30 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266767.846866833, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:04:30 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 23:04:30 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266767.846866833, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-28 23:04:30 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 23:04:30 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 23:04:30 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 23:04:30 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 23:04:30 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 23:04:30 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 23:04:30 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:30 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:30 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:30 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 21, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 21, 'workflow_status': 'running'}
2025-06-28 23:04:30 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 04f0a791-6988-49e0-bccc-1edde814062f) using provided producer.
2025-06-28 23:04:30 - NodeExecutor - DEBUG - Added correlation_id 07aef36a-5366-4757-a0ef-2c34a30e5991 to payload
2025-06-28 23:04:30 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 23:04:30 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '04f0a791-6988-49e0-bccc-1edde814062f', 'correlation_id': '07aef36a-5366-4757-a0ef-2c34a30e5991', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 23:04:30 - NodeExecutor - DEBUG - Request 04f0a791-6988-49e0-bccc-1edde814062f sent successfully using provided producer.
2025-06-28 23:04:30 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 04f0a791-6988-49e0-bccc-1edde814062f...
2025-06-28 23:04:31 - NodeExecutor - DEBUG - Result consumer received message: Offset=890
2025-06-28 23:04:31 - NodeExecutor - DEBUG - Received valid result for request_id 04f0a791-6988-49e0-bccc-1edde814062f
2025-06-28 23:04:31 - NodeExecutor - INFO - Result received for request 04f0a791-6988-49e0-bccc-1edde814062f.
2025-06-28 23:04:31 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-28 23:04:31 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 22, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:31 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 22, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 23:04:31 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751132071.4727712}}
2025-06-28 23:04:31 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 23:04:32 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 23:04:32 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:32 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:32 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 23:04:32 - LoopExecutor - INFO - 🚨 async notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-28 23:04:32 - LoopExecutor - DEBUG - 🔔 Received completion notification for transition: transition-CombineTextComponent-*************
2025-06-28 23:04:32 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:32 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.58 seconds
2025-06-28 23:04:32 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 23, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:32 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Completed transition in 2.58 seconds', 'message': 'Transition completed in 2.58 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 23, 'workflow_status': 'running'}
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:32 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-28 23:04:32 - LoopExecutor - DEBUG - 🔗 Adding next transitions to orchestration engine queue: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:32 - StateManager - INFO - Added transitions to pending: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:32 - StateManager - DEBUG - Updated pending transitions: {'transition-MCP_script-generation-mcp_script_generate-*************'}
2025-06-28 23:04:32 - LoopExecutor - DEBUG - 🚀 Creating new orchestration engine execution task to process transitions
2025-06-28 23:04:32 - LoopExecutor - DEBUG - ✅ Started transition chain from transition-CombineTextComponent-*************
2025-06-28 23:04:32 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:32 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:32 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 23:04:32 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 23:04:32 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 23:04:32 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 23:04:32 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:07aef36a-5366-4757-a0ef-2c34a30e5991'
2025-06-28 23:04:33 - RedisManager - DEBUG - Set key 'workflow_state:07aef36a-5366-4757-a0ef-2c34a30e5991' with TTL of 600 seconds
2025-06-28 23:04:33 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 07aef36a-5366-4757-a0ef-2c34a30e5991. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:33 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 23:04:33 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 23:04:33 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 23:04:33 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 23:04:33 - StateManager - INFO - Terminated: False
2025-06-28 23:04:33 - StateManager - INFO - Pending transitions (0): []
2025-06-28 23:04:33 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 23:04:33 - StateManager - INFO - Completed transitions (0): []
2025-06-28 23:04:33 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 23:04:33 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:04:33 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:04:33 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:04:33 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:04:33 - StateManager - INFO - Workflow paused: False
2025-06-28 23:04:33 - StateManager - INFO - ==============================
2025-06-28 23:04:33 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 23:04:33 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 24, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:33 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 24, 'workflow_status': 'running'}
2025-06-28 23:04:33 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 23:04:33 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-28 23:04:33 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 23:04:33 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 23:04:33 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 23:04:33 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 23:04:33 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 23:04:33 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 23:04:33 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:04:33 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:04:33 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 25, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:33 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 25, 'workflow_status': 'running'}
2025-06-28 23:04:33 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 23:04:33 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 23:04:33 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 23:04:33 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 23:04:33 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 23:04:33 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 23:04:33 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-28 23:04:33 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-28 23:04:33 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-28 23:04:33 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-28 23:04:33 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-28 23:04:33 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:04:33 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-28 23:04:33 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-28 23:04:33 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-28 23:04:33 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-28 23:04:33 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-28 23:04:33 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-28 23:04:33 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-28 23:04:33 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-28 23:04:33 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:33 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-28 23:04:33 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain
2025-06-28 23:04:33 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 23:04:33 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 23:04:33 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 23:04:33 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:33 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:33 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 23:04:33 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 23:04:34 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 23:04:34 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 23:04:34 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:34 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:34 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0'}
2025-06-28 23:04:34 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266773.508368791, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:35 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 23:04:35 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 23:04:35 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:35 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:35 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 23:04:35 - LoopExecutor - DEBUG - 💾 Stored loop iteration data under transition ID: transition-LoopNode-*************
2025-06-28 23:04:35 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-28 23:04:35 - LoopExecutor - DEBUG - 🚀 Starting entry transition: transition-CombineTextComponent-*************
2025-06-28 23:04:35 - LoopExecutor - DEBUG - 🎯 Executing transition transition-CombineTextComponent-************* through orchestration engine
2025-06-28 23:04:35 - LoopExecutor - INFO - 🚀 REAL EXECUTION: Starting transition-CombineTextComponent-************* via transition handler
2025-06-28 23:04:35 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 23:04:35 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 26, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:35 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 26, 'workflow_status': 'running'}
2025-06-28 23:04:35 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 23:04:35 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 23:04:35 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 23:04:35 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 23:04:35 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 23:04:35 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 23:04:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 23:04:36 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 23:04:36 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 23:04:36 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266773.508368791, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:04:36 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 23:04:36 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266773.508368791, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-28 23:04:36 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 23:04:36 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 23:04:36 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 23:04:36 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 23:04:36 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 23:04:36 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 23:04:36 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:36 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:36 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:36 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 27, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:36 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 27, 'workflow_status': 'running'}
2025-06-28 23:04:36 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: d02b593e-1f49-4b66-9d06-0a548674589a) using provided producer.
2025-06-28 23:04:36 - NodeExecutor - DEBUG - Added correlation_id 07aef36a-5366-4757-a0ef-2c34a30e5991 to payload
2025-06-28 23:04:36 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 23:04:36 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': 'd02b593e-1f49-4b66-9d06-0a548674589a', 'correlation_id': '07aef36a-5366-4757-a0ef-2c34a30e5991', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 23:04:36 - NodeExecutor - DEBUG - Request d02b593e-1f49-4b66-9d06-0a548674589a sent successfully using provided producer.
2025-06-28 23:04:36 - NodeExecutor - DEBUG - Waiting indefinitely for result for request d02b593e-1f49-4b66-9d06-0a548674589a...
2025-06-28 23:04:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 23:04:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 23:04:37 - NodeExecutor - DEBUG - Result consumer received message: Offset=891
2025-06-28 23:04:37 - NodeExecutor - DEBUG - Received valid result for request_id d02b593e-1f49-4b66-9d06-0a548674589a
2025-06-28 23:04:37 - NodeExecutor - INFO - Result received for request d02b593e-1f49-4b66-9d06-0a548674589a.
2025-06-28 23:04:37 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-28 23:04:37 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 28, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:37 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 28, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 23:04:37 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751132077.256598}}
2025-06-28 23:04:37 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 23:04:38 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 23:04:38 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:38 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:38 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 23:04:38 - LoopExecutor - INFO - 🚨 async notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-28 23:04:38 - LoopExecutor - DEBUG - 🔔 Received completion notification for transition: transition-CombineTextComponent-*************
2025-06-28 23:04:38 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:38 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.63 seconds
2025-06-28 23:04:38 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 29, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:38 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Completed transition in 2.63 seconds', 'message': 'Transition completed in 2.63 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 29, 'workflow_status': 'running'}
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-28 23:04:38 - LoopExecutor - DEBUG - 🔗 Adding next transitions to orchestration engine queue: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:38 - StateManager - INFO - Added transitions to pending: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:38 - StateManager - DEBUG - Updated pending transitions: {'transition-MCP_script-generation-mcp_script_generate-*************'}
2025-06-28 23:04:38 - LoopExecutor - DEBUG - 🚀 Creating new orchestration engine execution task to process transitions
2025-06-28 23:04:38 - LoopExecutor - DEBUG - ✅ Started transition chain from transition-CombineTextComponent-*************
2025-06-28 23:04:38 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:38 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:38 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 23:04:38 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 23:04:38 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 23:04:38 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 23:04:38 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:07aef36a-5366-4757-a0ef-2c34a30e5991'
2025-06-28 23:04:38 - RedisManager - DEBUG - Set key 'workflow_state:07aef36a-5366-4757-a0ef-2c34a30e5991' with TTL of 600 seconds
2025-06-28 23:04:38 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 07aef36a-5366-4757-a0ef-2c34a30e5991. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:38 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 23:04:38 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 23:04:38 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 23:04:38 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 23:04:38 - StateManager - INFO - Terminated: False
2025-06-28 23:04:38 - StateManager - INFO - Pending transitions (0): []
2025-06-28 23:04:38 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 23:04:38 - StateManager - INFO - Completed transitions (0): []
2025-06-28 23:04:38 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 23:04:38 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:04:38 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:04:38 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:04:38 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:04:38 - StateManager - INFO - Workflow paused: False
2025-06-28 23:04:38 - StateManager - INFO - ==============================
2025-06-28 23:04:38 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 23:04:38 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 30, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:38 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 30, 'workflow_status': 'running'}
2025-06-28 23:04:38 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 23:04:38 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 23:04:38 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 23:04:38 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:04:38 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:04:38 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 31, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:38 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 31, 'workflow_status': 'running'}
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 23:04:38 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 23:04:38 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 23:04:38 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-28 23:04:38 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-28 23:04:38 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-28 23:04:38 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-28 23:04:38 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-28 23:04:38 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:04:38 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-28 23:04:38 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-28 23:04:38 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-28 23:04:38 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-28 23:04:38 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-28 23:04:38 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-28 23:04:38 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-28 23:04:38 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-28 23:04:38 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:38 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-28 23:04:38 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain
2025-06-28 23:04:38 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 23:04:39 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 23:04:39 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 23:04:39 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:39 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:39 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 23:04:39 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 23:04:39 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 23:04:40 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 23:04:40 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:40 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:40 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0'}
2025-06-28 23:04:40 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266779.252013625, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:40 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 23:04:41 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 23:04:41 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:41 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:41 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 23:04:41 - LoopExecutor - DEBUG - 💾 Stored loop iteration data under transition ID: transition-LoopNode-*************
2025-06-28 23:04:41 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-28 23:04:41 - LoopExecutor - DEBUG - 🚀 Starting entry transition: transition-CombineTextComponent-*************
2025-06-28 23:04:41 - LoopExecutor - DEBUG - 🎯 Executing transition transition-CombineTextComponent-************* through orchestration engine
2025-06-28 23:04:41 - LoopExecutor - INFO - 🚀 REAL EXECUTION: Starting transition-CombineTextComponent-************* via transition handler
2025-06-28 23:04:41 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 23:04:41 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 32, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:41 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 32, 'workflow_status': 'running'}
2025-06-28 23:04:41 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 23:04:41 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 23:04:41 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 23:04:41 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 23:04:41 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 23:04:41 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 23:04:41 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 23:04:41 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266779.252013625, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:04:41 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 23:04:41 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266779.252013625, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-28 23:04:41 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 23:04:41 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 23:04:41 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 23:04:41 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 23:04:41 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 23:04:41 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 23:04:41 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:41 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:41 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:04:41 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 33, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:41 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 33, 'workflow_status': 'running'}
2025-06-28 23:04:41 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: f59e50f5-025e-4590-9acf-9687b1917df8) using provided producer.
2025-06-28 23:04:41 - NodeExecutor - DEBUG - Added correlation_id 07aef36a-5366-4757-a0ef-2c34a30e5991 to payload
2025-06-28 23:04:41 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 23:04:41 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': 'f59e50f5-025e-4590-9acf-9687b1917df8', 'correlation_id': '07aef36a-5366-4757-a0ef-2c34a30e5991', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 23:04:41 - NodeExecutor - DEBUG - Request f59e50f5-025e-4590-9acf-9687b1917df8 sent successfully using provided producer.
2025-06-28 23:04:41 - NodeExecutor - DEBUG - Waiting indefinitely for result for request f59e50f5-025e-4590-9acf-9687b1917df8...
2025-06-28 23:04:42 - NodeExecutor - DEBUG - Result consumer received message: Offset=892
2025-06-28 23:04:42 - NodeExecutor - DEBUG - Received valid result for request_id f59e50f5-025e-4590-9acf-9687b1917df8
2025-06-28 23:04:42 - NodeExecutor - INFO - Result received for request f59e50f5-025e-4590-9acf-9687b1917df8.
2025-06-28 23:04:42 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-28 23:04:42 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 34, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:42 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 34, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 23:04:42 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751132082.935355}}
2025-06-28 23:04:43 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 23:04:43 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 23:04:43 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:43 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:04:43 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'current_iteration', 'transition-LoopNode-*************', 'loop_iteration_0'}
2025-06-28 23:04:43 - LoopExecutor - INFO - 🚨 async notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-28 23:04:43 - LoopExecutor - DEBUG - 🔔 Received completion notification for transition: transition-CombineTextComponent-*************
2025-06-28 23:04:43 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:43 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.56 seconds
2025-06-28 23:04:43 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 35, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:43 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Completed transition in 2.56 seconds', 'message': 'Transition completed in 2.56 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 35, 'workflow_status': 'running'}
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:43 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-28 23:04:43 - LoopExecutor - DEBUG - 🔗 Adding next transitions to orchestration engine queue: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:43 - StateManager - INFO - Added transitions to pending: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:43 - StateManager - DEBUG - Updated pending transitions: {'transition-MCP_script-generation-mcp_script_generate-*************'}
2025-06-28 23:04:43 - LoopExecutor - DEBUG - 🚀 Creating new orchestration engine execution task to process transitions
2025-06-28 23:04:43 - LoopExecutor - DEBUG - ✅ Started transition chain from transition-CombineTextComponent-*************
2025-06-28 23:04:43 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:43 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:43 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 23:04:43 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 23:04:43 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 23:04:43 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 23:04:44 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:07aef36a-5366-4757-a0ef-2c34a30e5991'
2025-06-28 23:04:44 - RedisManager - DEBUG - Set key 'workflow_state:07aef36a-5366-4757-a0ef-2c34a30e5991' with TTL of 600 seconds
2025-06-28 23:04:44 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 07aef36a-5366-4757-a0ef-2c34a30e5991. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:04:44 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 23:04:44 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 23:04:44 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 23:04:44 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 23:04:44 - StateManager - INFO - Terminated: False
2025-06-28 23:04:44 - StateManager - INFO - Pending transitions (0): []
2025-06-28 23:04:44 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 23:04:44 - StateManager - INFO - Completed transitions (0): []
2025-06-28 23:04:44 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 23:04:44 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:04:44 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:04:44 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:04:44 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:04:44 - StateManager - INFO - Workflow paused: False
2025-06-28 23:04:44 - StateManager - INFO - ==============================
2025-06-28 23:04:44 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 23:04:44 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 36, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:44 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 36, 'workflow_status': 'running'}
2025-06-28 23:04:44 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 23:04:44 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-28 23:04:44 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 23:04:44 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 23:04:44 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 23:04:44 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 23:04:44 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 23:04:44 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 23:04:44 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:04:44 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:04:44 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 37, corr_id 07aef36a-5366-4757-a0ef-2c34a30e5991):
2025-06-28 23:04:44 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 37, 'workflow_status': 'running'}
2025-06-28 23:04:44 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 23:04:44 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 23:04:44 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 23:04:44 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 23:04:44 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 23:04:44 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 23:04:44 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-28 23:04:44 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-28 23:04:44 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-28 23:04:44 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-28 23:04:44 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-28 23:04:44 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:04:44 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-28 23:04:44 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-28 23:04:44 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-28 23:04:44 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-28 23:04:44 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-28 23:04:44 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-28 23:04:44 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-28 23:04:44 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-28 23:04:44 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:04:44 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-28 23:04:44 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain
2025-06-28 23:04:44 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 23:04:44 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 23:04:45 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:04:45 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-28 23:04:45 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-28 23:04:45 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-28 23:04:45 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-28 23:04:45 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-28 23:04:45 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-28 23:04:45 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-28 23:04:45 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-28 23:04:45 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-28 23:04:45 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-28 23:04:45 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-28 23:04:45 - EnhancedWorkflowEngine - WARNING - Workflow 07aef36a-5366-4757-a0ef-2c34a30e5991 execution was cancelled!
2025-06-28 23:04:45 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 23:04:45 - EnhancedWorkflowEngine - WARNING - Workflow 07aef36a-5366-4757-a0ef-2c34a30e5991 execution was cancelled!
2025-06-28 23:04:45 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 23:04:45 - EnhancedWorkflowEngine - WARNING - Workflow 07aef36a-5366-4757-a0ef-2c34a30e5991 execution was cancelled!
2025-06-28 23:04:45 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 23:04:45 - EnhancedWorkflowEngine - WARNING - Workflow 07aef36a-5366-4757-a0ef-2c34a30e5991 execution was cancelled!
2025-06-28 23:04:45 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 23:04:45 - EnhancedWorkflowEngine - WARNING - Workflow 07aef36a-5366-4757-a0ef-2c34a30e5991 execution was cancelled!
2025-06-28 23:04:45 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 23:04:45 - EnhancedWorkflowEngine - WARNING - Workflow 07aef36a-5366-4757-a0ef-2c34a30e5991 execution was cancelled!
2025-06-28 23:04:45 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 23:04:45 - EnhancedWorkflowEngine - WARNING - Workflow 07aef36a-5366-4757-a0ef-2c34a30e5991 execution was cancelled!
2025-06-28 23:04:45 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 23:04:45 - KafkaWorkflowConsumer - WARNING - Workflow execution for 'feda07bf-a91e-4004-80cb-72416cdb5a43' was cancelled
2025-06-28 23:04:45 - KafkaWorkflowConsumer - INFO - Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' final status: cancelled, result: Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' execution was cancelled
2025-06-28 23:04:45 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 07aef36a-5366-4757-a0ef-2c34a30e5991, response: {'status': 'Workflow Cancelled', 'result': "Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' execution was cancelled", 'workflow_status': 'cancelled'}
2025-06-28 23:04:45 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 07aef36a-5366-4757-a0ef-2c34a30e5991 
2025-06-28 23:04:45 - Main - ERROR - Shutting down due to keyboard interrupt...
