2025-06-28 19:58:46 - Main - INFO - Starting Server
2025-06-28 19:58:46 - Main - INFO - Connection at: **************:9092
2025-06-28 19:58:46 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 19:58:46 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-28 19:58:46 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 19:58:46 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 19:58:46 - <PERSON><PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 19:58:47 - <PERSON>isManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 19:58:47 - <PERSON>isManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
