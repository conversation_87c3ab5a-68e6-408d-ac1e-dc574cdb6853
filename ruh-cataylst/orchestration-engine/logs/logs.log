2025-06-28 22:41:57 - Main - INFO - Starting Server
2025-06-28 22:41:57 - Main - INFO - Connection at: **************:9092
2025-06-28 22:41:57 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 22:41:57 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-28 22:41:57 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 22:41:57 - Ka<PERSON>kaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 22:41:57 - <PERSON><PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 22:41:58 - <PERSON><PERSON>Manager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 22:41:58 - Red<PERSON>Manager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
