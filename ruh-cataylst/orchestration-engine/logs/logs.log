2025-06-28 22:19:06 - Main - INFO - Starting Server
2025-06-28 22:19:06 - Main - INFO - Connection at: **************:9092
2025-06-28 22:19:06 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 22:19:06 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-28 22:19:06 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 22:19:06 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 22:19:06 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 22:19:08 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 22:19:08 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 22:19:10 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 22:19:12 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-28 22:19:12 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-28 22:19:14 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 22:19:15 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-28 22:19:15 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 22:19:16 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 22:19:16 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 22:19:18 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 22:19:18 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-28 22:19:18 - RedisEventListener - INFO - Redis event listener started
2025-06-28 22:19:18 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-28 22:19:18 - StateManager - DEBUG - Using provided database connections
2025-06-28 22:19:18 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 22:19:18 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 22:19:18 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 22:19:18 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-28 22:19:19 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 22:19:19 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 22:19:19 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-28 22:19:19 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-28 22:19:19 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-28 22:19:19 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-28 22:19:22 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-28 22:19:22 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-28 22:19:22 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-28 22:19:35 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-28 22:19:41 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-28 22:19:41 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-28 22:19:41 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-28 22:19:47 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-28 22:19:47 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-28 22:19:47 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-28 22:19:48 - NodeExecutor - DEBUG - Result consumer received message: Offset=873
2025-06-28 22:19:48 - NodeExecutor - WARNING - Received result for unknown or timed-out request_id: 2ba145e0-6780-4773-a5cc-10e261bf3b89
2025-06-28 22:19:54 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-28 22:19:54 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-28 22:19:54 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1019
2025-06-28 22:19:54 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': **********, 'task_type': 'workflow', 'data': {'workflow_id': 'feda07bf-a91e-4004-80cb-72416cdb5a43', 'payload': {'user_dependent_fields': ['iteration_list'], 'user_payload_template': {'iteration_list': {'value': '["blockchain","automation","healthcare"]', 'transition_id': 'LoopNode-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-06-28 22:19:54 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 22:19:54 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 22:19:54 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-28 22:19:54 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow LOOP TEST DO NOT DELETE retrieved successfully",
  "workflow": {
    "id": "feda07bf-a91e-4004-80cb-72416cdb5a43",
    "name": "LOOP TEST DO NOT DELETE",
    "description": "LOOP_TEST_DO_NOT_DELETE",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/16a976d6-6669-4cb4-8207-4f9b8626a0b8.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/91cb74a1-1bfd-42fa-b59d-58773b35d33a.json",
    "start_nodes": [
      {
        "field": "iteration_list",
        "type": "list",
        "transition_id": "transition-LoopNode-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-24T11:13:46.129953",
    "updated_at": "2025-06-28T14:13:59.013729",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "LoopNode",
        "display_name": "For Each Loop",
        "type": "component",
        "transition_id": "transition-LoopNode-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1751052569332"
      },
      {
        "name": "MCP_script-generation-mcp_script_generate",
        "id": "748a8221-d7d9-4352-93ae-00700f4d28b1",
        "transition_id": "transition-MCP_script-generation-mcp_script_generate-*************",
        "type": "mcp",
        "display_name": "script-generation-mcp - script_generate",
        "data": {
          "input_schema": {
            "$defs": {
              "Keywords": {
                "properties": {
                  "time": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Time"
                  },
                  "objective": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Objective"
                  },
                  "audience": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Audience"
                  },
                  "gender": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Gender"
                  },
                  "tone": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Tone"
                  },
                  "speakers": {
                    "anyOf": [
                      {
                        "items": {
                          "type": "string"
                        },
                        "type": "array"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Speakers"
                  }
                },
                "title": "Keywords",
                "type": "object"
              },
              "ScriptType": {
                "enum": [
                  "VIDEO",
                  "TOPIC",
                  "SCRIPT",
                  "BLOG",
                  "AI"
                ],
                "title": "ScriptType",
                "type": "string"
              },
              "VideoType": {
                "enum": [
                  "SHORT",
                  "LONG"
                ],
                "title": "VideoType",
                "type": "string"
              }
            },
            "properties": {
              "topic": {
                "title": "Topic",
                "type": "string"
              },
              "script_type": {
                "$ref": "#/$defs/ScriptType",
                "default": "TOPIC"
              },
              "keywords": {
                "$ref": "#/$defs/Keywords"
              },
              "video_type": {
                "$ref": "#/$defs/VideoType",
                "default": "SHORT"
              },
              "link": {
                "anyOf": [
                  {
                    "format": "uri",
                    "maxLength": 2083,
                    "minLength": 1,
                    "type": "string"
                  },
                  {
                    "type": "null"
                  }
                ],
                "default": null,
                "title": "Link"
              }
            },
            "required": [
              "topic"
            ],
            "title": "GenerateScriptInput",
            "type": "object"
          },
          "output_schema": {
            "properties": {
              "title": {
                "type": "string",
                "description": "Title of the generated script",
                "title": "title"
              },
              "script": {
                "type": "string",
                "description": "The generated script",
                "title": "script"
              },
              "script_type": {
                "type": "string",
                "description": "Type of the script",
                "title": "script_type"
              },
              "video_type": {
                "type": "string",
                "description": "The type of video",
                "title": "video_type"
              },
              "link": {
                "type": "string",
                "format": "uri",
                "description": "Optional link for the script",
                "title": "link"
              }
            }
          }
        }
      }
    ],
    "is_updated": true
  }
}
2025-06-28 22:19:54 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for feda07bf-a91e-4004-80cb-72416cdb5a43 - server_script_path is optional
2025-06-28 22:19:54 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-28 22:19:54 - StateManager - DEBUG - Using global database connections from initializer
2025-06-28 22:19:54 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 22:19:54 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 22:19:54 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 22:19:55 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 22:19:55 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 22:19:55 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-28 22:19:55 - StateManager - DEBUG - Using provided database connections
2025-06-28 22:19:55 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 22:19:55 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 22:19:55 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 22:19:56 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 22:19:56 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 22:19:56 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-28 22:19:56 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751052569332: ['transition-LoopNode-*************']
2025-06-28 22:19:56 - StateManager - INFO - Built dependency map for 3 transitions
2025-06-28 22:19:56 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-28 22:19:56 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751052569332 depends on: ['transition-LoopNode-*************']
2025-06-28 22:19:56 - MCPToolExecutor - DEBUG - Set correlation ID to: b1a2883e-0cd2-4921-9da9-71f9431cb499
2025-06-28 22:19:56 - EnhancedWorkflowEngine - DEBUG - Set correlation_id b1a2883e-0cd2-4921-9da9-71f9431cb499 in tool_executor
2025-06-28 22:19:56 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 22:19:56 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-06-28 22:19:56 - NodeExecutor - DEBUG - Set correlation ID to: b1a2883e-0cd2-4921-9da9-71f9431cb499
2025-06-28 22:19:56 - EnhancedWorkflowEngine - DEBUG - Set correlation_id b1a2883e-0cd2-4921-9da9-71f9431cb499 in node_executor
2025-06-28 22:19:56 - AgentExecutor - DEBUG - Set correlation ID to: b1a2883e-0cd2-4921-9da9-71f9431cb499
2025-06-28 22:19:56 - EnhancedWorkflowEngine - DEBUG - Set correlation_id b1a2883e-0cd2-4921-9da9-71f9431cb499 in agent_executor
2025-06-28 22:19:56 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 22:19:56 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-06-28 22:19:56 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-28 22:19:56 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: b1a2883e-0cd2-4921-9da9-71f9431cb499
2025-06-28 22:19:56 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: b1a2883e-0cd2-4921-9da9-71f9431cb499
2025-06-28 22:19:56 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b1a2883e-0cd2-4921-9da9-71f9431cb499, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-28 22:19:56 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 22:19:56 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 22:19:56 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 22:19:56 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 22:19:57 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:b1a2883e-0cd2-4921-9da9-71f9431cb499'
2025-06-28 22:19:57 - RedisManager - DEBUG - Set key 'workflow_state:b1a2883e-0cd2-4921-9da9-71f9431cb499' with TTL of 600 seconds
2025-06-28 22:19:57 - StateManager - INFO - Workflow state saved to Redis for workflow ID: b1a2883e-0cd2-4921-9da9-71f9431cb499. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 22:19:57 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 22:19:57 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 22:19:57 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 22:19:57 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 22:19:57 - StateManager - INFO - Terminated: False
2025-06-28 22:19:57 - StateManager - INFO - Pending transitions (0): []
2025-06-28 22:19:57 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 22:19:57 - StateManager - INFO - Completed transitions (0): []
2025-06-28 22:19:57 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 22:19:57 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 22:19:57 - StateManager - INFO - Workflow status: inactive
2025-06-28 22:19:57 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 22:19:57 - StateManager - INFO - Workflow status: inactive
2025-06-28 22:19:57 - StateManager - INFO - Workflow paused: False
2025-06-28 22:19:57 - StateManager - INFO - ==============================
2025-06-28 22:19:57 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 22:19:57 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id b1a2883e-0cd2-4921-9da9-71f9431cb499):
2025-06-28 22:19:57 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b1a2883e-0cd2-4921-9da9-71f9431cb499, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-28 22:19:57 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 22:19:57 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 22:19:57 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 22:19:57 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 22:19:57 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 22:19:57 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 22:19:57 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 22:19:57 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 22:19:57 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 22:19:57 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id b1a2883e-0cd2-4921-9da9-71f9431cb499):
2025-06-28 22:19:57 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b1a2883e-0cd2-4921-9da9-71f9431cb499, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-28 22:19:57 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 22:19:57 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 22:19:57 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 22:19:57 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 22:19:57 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 22:19:57 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 22:19:57 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 22:19:57 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 22:19:58 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 22:19:58 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 22:19:58 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 22:19:58 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 22:19:58 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 22:19:58 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 22:19:58 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 22:19:59 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 22:19:59 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 22:19:59 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 22:19:59 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0'}
2025-06-28 22:19:59 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 264098.139790291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 22:19:59 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 22:20:00 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 22:20:00 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 22:20:00 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 22:20:00 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'transition-LoopNode-*************'}
2025-06-28 22:20:00 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 22:20:00 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id b1a2883e-0cd2-4921-9da9-71f9431cb499):
2025-06-28 22:20:00 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b1a2883e-0cd2-4921-9da9-71f9431cb499, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 2, 'workflow_status': 'running'}
2025-06-28 22:20:00 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 22:20:00 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 22:20:00 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 22:20:00 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 22:20:00 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 22:20:00 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 22:20:00 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 22:20:00 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 264098.139790291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 22:20:00 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 22:20:00 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 264098.139790291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-28 22:20:00 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 22:20:00 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 22:20:00 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 22:20:00 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 22:20:00 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 22:20:00 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 22:20:00 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 22:20:00 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 22:20:00 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 22:20:00 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id b1a2883e-0cd2-4921-9da9-71f9431cb499):
2025-06-28 22:20:00 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b1a2883e-0cd2-4921-9da9-71f9431cb499, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 3, 'workflow_status': 'running'}
2025-06-28 22:20:00 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 56804e40-0e7a-4e31-b9a7-27e3f5f4846c) using provided producer.
2025-06-28 22:20:00 - NodeExecutor - DEBUG - Added correlation_id b1a2883e-0cd2-4921-9da9-71f9431cb499 to payload
2025-06-28 22:20:00 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 22:20:00 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '56804e40-0e7a-4e31-b9a7-27e3f5f4846c', 'correlation_id': 'b1a2883e-0cd2-4921-9da9-71f9431cb499', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 22:20:00 - NodeExecutor - DEBUG - Request 56804e40-0e7a-4e31-b9a7-27e3f5f4846c sent successfully using provided producer.
2025-06-28 22:20:00 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 56804e40-0e7a-4e31-b9a7-27e3f5f4846c...
2025-06-28 22:20:01 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1019, corr_id: b1a2883e-0cd2-4921-9da9-71f9431cb499
2025-06-28 22:20:01 - NodeExecutor - DEBUG - Result consumer received message: Offset=874
2025-06-28 22:20:01 - NodeExecutor - DEBUG - Received valid result for request_id 56804e40-0e7a-4e31-b9a7-27e3f5f4846c
2025-06-28 22:20:01 - NodeExecutor - INFO - Result received for request 56804e40-0e7a-4e31-b9a7-27e3f5f4846c.
2025-06-28 22:20:01 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-28 22:20:01 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id b1a2883e-0cd2-4921-9da9-71f9431cb499):
2025-06-28 22:20:01 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b1a2883e-0cd2-4921-9da9-71f9431cb499, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 4, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 22:20:01 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751129401.978029}}
2025-06-28 22:20:02 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 22:20:02 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 22:20:02 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 22:20:02 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 22:20:02 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************'}
2025-06-28 22:20:02 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 22:20:02 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.67 seconds
2025-06-28 22:20:02 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id b1a2883e-0cd2-4921-9da9-71f9431cb499):
2025-06-28 22:20:02 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b1a2883e-0cd2-4921-9da9-71f9431cb499, response: {'result': 'Completed transition in 2.67 seconds', 'message': 'Transition completed in 2.67 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 5, 'workflow_status': 'running'}
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 22:20:02 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-28 22:20:15 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 22:20:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 22:20:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 22:20:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 22:21:15 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 22:21:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 22:21:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 22:21:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 22:21:20 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-28 22:21:20 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-28 22:21:20 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-28 22:21:20 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-28 22:21:20 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-28 22:21:20 - EnhancedWorkflowEngine - WARNING - Workflow b1a2883e-0cd2-4921-9da9-71f9431cb499 execution was cancelled!
2025-06-28 22:21:20 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 22:21:20 - KafkaWorkflowConsumer - WARNING - Workflow execution for 'feda07bf-a91e-4004-80cb-72416cdb5a43' was cancelled
2025-06-28 22:21:20 - KafkaWorkflowConsumer - INFO - Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' final status: cancelled, result: Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' execution was cancelled
2025-06-28 22:21:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b1a2883e-0cd2-4921-9da9-71f9431cb499, response: {'status': 'Workflow Cancelled', 'result': "Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' execution was cancelled", 'workflow_status': 'cancelled'}
2025-06-28 22:21:20 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: b1a2883e-0cd2-4921-9da9-71f9431cb499 
2025-06-28 22:21:20 - Main - ERROR - Shutting down due to keyboard interrupt...
