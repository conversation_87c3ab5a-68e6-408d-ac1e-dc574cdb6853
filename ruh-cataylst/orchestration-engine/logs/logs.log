2025-06-28 18:41:12 - Main - INFO - Starting Server
2025-06-28 18:41:12 - Main - INFO - Connection at: **************:9092
2025-06-28 18:41:12 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 18:41:12 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-28 18:41:12 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 18:41:12 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 18:41:12 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 18:41:14 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 18:41:14 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 18:41:16 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 18:41:18 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-28 18:41:18 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-28 18:41:20 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 18:41:21 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-28 18:41:21 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 18:41:22 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 18:41:22 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 18:41:24 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 18:41:24 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-28 18:41:24 - RedisEventListener - INFO - Redis event listener started
2025-06-28 18:41:24 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-28 18:41:24 - StateManager - DEBUG - Using provided database connections
2025-06-28 18:41:24 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 18:41:24 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 18:41:24 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 18:41:25 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-28 18:41:25 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 18:41:25 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 18:41:25 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-28 18:41:25 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-28 18:41:25 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-28 18:41:25 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-28 18:41:28 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-28 18:41:28 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-28 18:41:28 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-28 18:41:41 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-28 18:41:48 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-28 18:41:48 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-28 18:41:48 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-28 18:41:54 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-28 18:41:54 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-28 18:41:54 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-28 18:42:01 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-28 18:42:01 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-28 18:42:01 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1007
2025-06-28 18:42:01 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': **********, 'task_type': 'workflow', 'data': {'workflow_id': 'feda07bf-a91e-4004-80cb-72416cdb5a43', 'payload': {'user_dependent_fields': ['iteration_list'], 'user_payload_template': {'iteration_list': {'value': '["blockchain","automation","healthcare"]', 'transition_id': 'LoopNode-1750863512419'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-06-28 18:42:01 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 18:42:01 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 18:42:02 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-28 18:42:02 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow LOOP TEST DO NOT DELETE retrieved successfully",
  "workflow": {
    "id": "feda07bf-a91e-4004-80cb-72416cdb5a43",
    "name": "LOOP TEST DO NOT DELETE",
    "description": "LOOP_TEST_DO_NOT_DELETE",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/dfbb35aa-5dac-4505-b8ab-e29a2853971a.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/e1da74c2-0744-4dc2-b2c1-d46aca8a8f34.json",
    "start_nodes": [
      {
        "field": "iteration_list",
        "type": "list",
        "transition_id": "transition-LoopNode-1750863512419"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-24T11:13:46.129953",
    "updated_at": "2025-06-28T13:04:23.189580",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-1750769520925"
      },
      {
        "name": "LoopNode",
        "display_name": "For Each Loop",
        "type": "component",
        "transition_id": "transition-LoopNode-1750863512419"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1751052569332"
      },
      {
        "name": "MCP_script-generation-mcp_script_generate",
        "id": "748a8221-d7d9-4352-93ae-00700f4d28b1",
        "transition_id": "transition-MCP_script-generation-mcp_script_generate-1751115762780",
        "type": "mcp",
        "display_name": "script-generation-mcp - script_generate",
        "data": {
          "input_schema": {
            "$defs": {
              "Keywords": {
                "properties": {
                  "time": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Time"
                  },
                  "objective": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Objective"
                  },
                  "audience": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Audience"
                  },
                  "gender": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Gender"
                  },
                  "tone": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Tone"
                  },
                  "speakers": {
                    "anyOf": [
                      {
                        "items": {
                          "type": "string"
                        },
                        "type": "array"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Speakers"
                  }
                },
                "title": "Keywords",
                "type": "object"
              },
              "ScriptType": {
                "enum": [
                  "VIDEO",
                  "TOPIC",
                  "SCRIPT",
                  "BLOG",
                  "AI"
                ],
                "title": "ScriptType",
                "type": "string"
              },
              "VideoType": {
                "enum": [
                  "SHORT",
                  "LONG"
                ],
                "title": "VideoType",
                "type": "string"
              }
            },
            "properties": {
              "topic": {
                "title": "Topic",
                "type": "string"
              },
              "script_type": {
                "$ref": "#/$defs/ScriptType",
                "default": "TOPIC"
              },
              "keywords": {
                "$ref": "#/$defs/Keywords"
              },
              "video_type": {
                "$ref": "#/$defs/VideoType",
                "default": "SHORT"
              },
              "link": {
                "anyOf": [
                  {
                    "format": "uri",
                    "maxLength": 2083,
                    "minLength": 1,
                    "type": "string"
                  },
                  {
                    "type": "null"
                  }
                ],
                "default": null,
                "title": "Link"
              }
            },
            "required": [
              "topic"
            ],
            "title": "GenerateScriptInput",
            "type": "object"
          },
          "output_schema": {
            "properties": {
              "title": {
                "type": "string",
                "description": "Title of the generated script",
                "title": "title"
              },
              "script": {
                "type": "string",
                "description": "The generated script",
                "title": "script"
              },
              "script_type": {
                "type": "string",
                "description": "Type of the script",
                "title": "script_type"
              },
              "video_type": {
                "type": "string",
                "description": "The type of video",
                "title": "video_type"
              },
              "link": {
                "type": "string",
                "format": "uri",
                "description": "Optional link for the script",
                "title": "link"
              }
            }
          }
        }
      }
    ],
    "is_updated": true
  }
}
2025-06-28 18:42:02 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for feda07bf-a91e-4004-80cb-72416cdb5a43 - server_script_path is optional
2025-06-28 18:42:02 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-28 18:42:02 - StateManager - DEBUG - Using global database connections from initializer
2025-06-28 18:42:02 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 18:42:02 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 18:42:02 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 18:42:03 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 18:42:03 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 18:42:03 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-28 18:42:03 - StateManager - DEBUG - Using provided database connections
2025-06-28 18:42:03 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 18:42:03 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 18:42:03 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 18:42:03 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 18:42:03 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 18:42:03 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751052569332: ['transition-LoopNode-1750863512419']
2025-06-28 18:42:03 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750769520925: ['transition-LoopNode-1750863512419']
2025-06-28 18:42:03 - StateManager - DEBUG - Extracted dependencies for transition transition-MCP_script-generation-mcp_script_generate-1751115762780: ['transition-CombineTextComponent-1750769520925']
2025-06-28 18:42:03 - StateManager - INFO - Built dependency map for 4 transitions
2025-06-28 18:42:03 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751052569332 depends on: ['transition-LoopNode-1750863512419']
2025-06-28 18:42:03 - StateManager - DEBUG - Transition transition-CombineTextComponent-1750769520925 depends on: ['transition-LoopNode-1750863512419']
2025-06-28 18:42:03 - StateManager - DEBUG - Transition transition-MCP_script-generation-mcp_script_generate-1751115762780 depends on: ['transition-CombineTextComponent-1750769520925']
2025-06-28 18:42:03 - MCPToolExecutor - DEBUG - Set correlation ID to: ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a
2025-06-28 18:42:03 - EnhancedWorkflowEngine - DEBUG - Set correlation_id ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a in tool_executor
2025-06-28 18:42:03 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 18:42:03 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-06-28 18:42:03 - NodeExecutor - DEBUG - Set correlation ID to: ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a
2025-06-28 18:42:03 - EnhancedWorkflowEngine - DEBUG - Set correlation_id ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a in node_executor
2025-06-28 18:42:03 - AgentExecutor - DEBUG - Set correlation ID to: ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a
2025-06-28 18:42:03 - EnhancedWorkflowEngine - DEBUG - Set correlation_id ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a in agent_executor
2025-06-28 18:42:03 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 18:42:03 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-06-28 18:42:03 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-28 18:42:03 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a
2025-06-28 18:42:03 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a
2025-06-28 18:42:04 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-28 18:42:04 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-1750863512419
2025-06-28 18:42:04 - StateManager - DEBUG - State: pending={'transition-LoopNode-1750863512419'}, waiting=set(), completed=set()
2025-06-28 18:42:04 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-1750863512419
2025-06-28 18:42:04 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-1750863512419'}
2025-06-28 18:42:04 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a'
2025-06-28 18:42:05 - RedisManager - DEBUG - Set key 'workflow_state:ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a' with TTL of 600 seconds
2025-06-28 18:42:05 - StateManager - INFO - Workflow state saved to Redis for workflow ID: ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:42:05 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 18:42:05 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 18:42:05 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-1750863512419'}
2025-06-28 18:42:05 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 18:42:05 - StateManager - INFO - Terminated: False
2025-06-28 18:42:05 - StateManager - INFO - Pending transitions (0): []
2025-06-28 18:42:05 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 18:42:05 - StateManager - INFO - Completed transitions (0): []
2025-06-28 18:42:05 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 18:42:05 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 18:42:05 - StateManager - INFO - Workflow status: inactive
2025-06-28 18:42:05 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 18:42:05 - StateManager - INFO - Workflow status: inactive
2025-06-28 18:42:05 - StateManager - INFO - Workflow paused: False
2025-06-28 18:42:05 - StateManager - INFO - ==============================
2025-06-28 18:42:05 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-1750863512419
2025-06-28 18:42:05 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a):
2025-06-28 18:42:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a, response: {'result': 'Starting execution of transition: transition-LoopNode-1750863512419', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-1750863512419', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-28 18:42:05 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-1750863512419' (type=initial, execution_type=loop)
2025-06-28 18:42:05 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 18:42:05 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 18:42:05 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-1750863512419
2025-06-28 18:42:05 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 18:42:05 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 18:42:05 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 18:42:05 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 18:42:05 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-1750863512419' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 18:42:05 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a):
2025-06-28 18:42:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a, response: {'transition_id': 'transition-LoopNode-1750863512419', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-28 18:42:05 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-1750863512419
2025-06-28 18:42:05 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-1750863512419
2025-06-28 18:42:05 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ["blockchain","automation","healthcare"]
2025-06-28 18:42:05 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-1750769520925 (has current_item/iteration indicators)
2025-06-28 18:42:05 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 18:42:05 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-1750769520925']
2025-06-28 18:42:05 - TransitionHandler - ERROR - Tool execution failed for tool 'LoopNode' (tool_id: 1) in node 'LoopNode' of transition 'transition-LoopNode-1750863512419': iteration_list must be an arrayTraceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 426, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 129, in execute_tool
    parsed_config = self.parse_loop_config(loop_config)
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 377, in parse_loop_config
    self._validate_new_schema_raw(loop_config)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1156, in _validate_new_schema_raw
    raise ValueError("iteration_list must be an array")
ValueError: iteration_list must be an array

2025-06-28 18:42:05 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a):
2025-06-28 18:42:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a, response: {'transition_id': 'transition-LoopNode-1750863512419', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: iteration_list must be an array', 'status': 'failed', 'sequence': 2, 'workflow_status': 'running'}
2025-06-28 18:42:05 - TransitionHandler - ERROR - Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array
2025-06-28 18:42:05 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array')]
2025-06-28 18:42:05 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-LoopNode-1750863512419: Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array
2025-06-28 18:42:05 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-LoopNode-1750863512419: NoneType: None

2025-06-28 18:42:05 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array
2025-06-28 18:42:05 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 426, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 129, in execute_tool
    parsed_config = self.parse_loop_config(loop_config)
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 377, in parse_loop_config
    self._validate_new_schema_raw(loop_config)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1156, in _validate_new_schema_raw
    raise ValueError("iteration_list must be an array")
ValueError: iteration_list must be an array

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 684, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 263, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 227, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array

2025-06-28 18:42:05 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array
2025-06-28 18:42:05 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 426, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 129, in execute_tool
    parsed_config = self.parse_loop_config(loop_config)
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 377, in parse_loop_config
    self._validate_new_schema_raw(loop_config)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1156, in _validate_new_schema_raw
    raise ValueError("iteration_list must be an array")
ValueError: iteration_list must be an array

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 684, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 280, in execute
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 263, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 227, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array

2025-06-28 18:42:05 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array
2025-06-28 18:42:05 - KafkaWorkflowConsumer - INFO - Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' final status: failed, result: Exception in workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43': Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array
2025-06-28 18:42:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a, response: {'status': 'failed', 'result': "Exception in workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43': Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array", 'workflow_status': 'failed', 'error': 'Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array', 'error_type': 'Exception'}
2025-06-28 18:42:05 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a 
2025-06-28 18:42:05 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1007, corr_id: ac622acb-fbdc-4cf6-aa3a-58e1dc94eb6a
