2025-06-28 20:18:45 - Main - INFO - Starting Server
2025-06-28 20:18:45 - Main - INFO - Connection at: **************:9092
2025-06-28 20:18:45 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 20:18:45 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-28 20:18:45 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 20:18:45 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 20:18:45 - Red<PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 20:18:47 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 20:18:47 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
