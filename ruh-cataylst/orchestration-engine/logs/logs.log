2025-06-28 22:54:33 - Main - INFO - Starting Server
2025-06-28 22:54:33 - Main - INFO - Connection at: **************:9092
2025-06-28 22:54:33 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 22:54:33 - <PERSON>de<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-28 22:54:33 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 22:54:33 - Ka<PERSON><PERSON>WorkflowConsumer - INFO - Initializing database connections...
2025-06-28 22:54:33 - <PERSON><PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 22:54:35 - <PERSON>isManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 22:54:35 - <PERSON>isManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
