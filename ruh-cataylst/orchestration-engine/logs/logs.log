2025-06-29 02:12:11 - Main - INFO - Starting Server
2025-06-29 02:12:11 - Main - INFO - Connection at: **************:9092
2025-06-29 02:12:11 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-29 02:12:11 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-29 02:12:11 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-29 02:12:11 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-29 02:12:11 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-29 02:12:13 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-29 02:12:13 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-29 02:12:14 - Red<PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-29 02:12:18 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-29 02:12:18 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-29 02:12:22 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-29 02:12:22 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-29 02:12:22 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-29 02:12:25 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-29 02:12:25 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-29 02:12:26 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-29 02:12:26 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-29 02:12:26 - RedisEventListener - INFO - Redis event listener started
2025-06-29 02:12:26 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-29 02:12:26 - StateManager - DEBUG - Using provided database connections
2025-06-29 02:12:26 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-29 02:12:26 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-29 02:12:26 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-29 02:12:27 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-29 02:12:27 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-29 02:12:27 - StateManager - INFO - WorkflowStateManager initialized
2025-06-29 02:12:27 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-29 02:12:27 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-29 02:12:27 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-29 02:12:27 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-29 02:12:30 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-29 02:12:30 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-29 02:12:30 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-29 02:12:34 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-29 02:12:42 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-29 02:12:42 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-29 02:12:42 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-29 02:12:50 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-29 02:12:50 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-29 02:12:50 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-29 02:12:56 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-29 02:12:56 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-29 02:12:56 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1042
2025-06-29 02:12:56 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': **********, 'task_type': 'workflow', 'data': {'workflow_id': 'feda07bf-a91e-4004-80cb-72416cdb5a43', 'payload': {'user_dependent_fields': ['iteration_list'], 'user_payload_template': {'iteration_list': {'value': '["blockchain","automation","healthcare"]', 'transition_id': 'LoopNode-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-06-29 02:12:56 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-29 02:12:56 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-29 02:12:57 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-29 02:12:57 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow LOOP TEST DO NOT DELETE retrieved successfully",
  "workflow": {
    "id": "feda07bf-a91e-4004-80cb-72416cdb5a43",
    "name": "LOOP TEST DO NOT DELETE",
    "description": "LOOP_TEST_DO_NOT_DELETE",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/43cc8cb2-8487-4fc7-9bde-2f85d9d77358.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/7d0adb6e-b505-44d2-829a-0c1b50fdd13e.json",
    "start_nodes": [
      {
        "field": "iteration_list",
        "type": "list",
        "transition_id": "transition-LoopNode-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-24T11:13:46.129953",
    "updated_at": "2025-06-28T20:42:03.816041",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "LoopNode",
        "display_name": "For Each Loop",
        "type": "component",
        "transition_id": "transition-LoopNode-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************"
      },
      {
        "name": "MCP_script-generation-mcp_script_generate",
        "id": "748a8221-d7d9-4352-93ae-00700f4d28b1",
        "transition_id": "transition-MCP_script-generation-mcp_script_generate-*************",
        "type": "mcp",
        "display_name": "script-generation-mcp - script_generate",
        "data": {
          "input_schema": {
            "$defs": {
              "Keywords": {
                "properties": {
                  "time": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Time"
                  },
                  "objective": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Objective"
                  },
                  "audience": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Audience"
                  },
                  "gender": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Gender"
                  },
                  "tone": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Tone"
                  },
                  "speakers": {
                    "anyOf": [
                      {
                        "items": {
                          "type": "string"
                        },
                        "type": "array"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Speakers"
                  }
                },
                "title": "Keywords",
                "type": "object"
              },
              "ScriptType": {
                "enum": [
                  "VIDEO",
                  "TOPIC",
                  "SCRIPT",
                  "BLOG",
                  "AI"
                ],
                "title": "ScriptType",
                "type": "string"
              },
              "VideoType": {
                "enum": [
                  "SHORT",
                  "LONG"
                ],
                "title": "VideoType",
                "type": "string"
              }
            },
            "properties": {
              "topic": {
                "title": "Topic",
                "type": "string"
              },
              "script_type": {
                "$ref": "#/$defs/ScriptType",
                "default": "TOPIC"
              },
              "keywords": {
                "$ref": "#/$defs/Keywords"
              },
              "video_type": {
                "$ref": "#/$defs/VideoType",
                "default": "SHORT"
              },
              "link": {
                "anyOf": [
                  {
                    "format": "uri",
                    "maxLength": 2083,
                    "minLength": 1,
                    "type": "string"
                  },
                  {
                    "type": "null"
                  }
                ],
                "default": null,
                "title": "Link"
              }
            },
            "required": [
              "topic"
            ],
            "title": "GenerateScriptInput",
            "type": "object"
          },
          "output_schema": {
            "properties": {
              "title": {
                "type": "string",
                "description": "Title of the generated script",
                "title": "title"
              },
              "script": {
                "type": "string",
                "description": "The generated script",
                "title": "script"
              },
              "script_type": {
                "type": "string",
                "description": "Type of the script",
                "title": "script_type"
              },
              "video_type": {
                "type": "string",
                "description": "The type of video",
                "title": "video_type"
              },
              "link": {
                "type": "string",
                "format": "uri",
                "description": "Optional link for the script",
                "title": "link"
              }
            }
          }
        }
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      }
    ],
    "is_updated": true
  }
}
2025-06-29 02:12:58 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for feda07bf-a91e-4004-80cb-72416cdb5a43 - server_script_path is optional
2025-06-29 02:12:58 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-29 02:12:58 - StateManager - DEBUG - Using global database connections from initializer
2025-06-29 02:12:58 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-29 02:12:58 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-29 02:12:58 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-29 02:12:58 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-29 02:12:58 - StateManager - INFO - WorkflowStateManager initialized
2025-06-29 02:12:58 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-29 02:12:58 - StateManager - DEBUG - Using provided database connections
2025-06-29 02:12:58 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-29 02:12:58 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-29 02:12:58 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-29 02:12:59 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-29 02:12:59 - StateManager - INFO - WorkflowStateManager initialized
2025-06-29 02:12:59 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-29 02:12:59 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-*************: ['transition-LoopNode-*************']
2025-06-29 02:12:59 - StateManager - DEBUG - Extracted dependencies for transition transition-MCP_script-generation-mcp_script_generate-*************: ['transition-CombineTextComponent-*************']
2025-06-29 02:12:59 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:12:59 - StateManager - INFO - Built dependency map for 5 transitions
2025-06-29 02:12:59 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-29 02:12:59 - StateManager - DEBUG - Transition transition-MergeDataComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-29 02:12:59 - StateManager - DEBUG - Transition transition-MCP_script-generation-mcp_script_generate-************* depends on: ['transition-CombineTextComponent-*************']
2025-06-29 02:12:59 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:12:59 - MCPToolExecutor - DEBUG - Set correlation ID to: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b
2025-06-29 02:12:59 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b in tool_executor
2025-06-29 02:12:59 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-29 02:12:59 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-06-29 02:12:59 - NodeExecutor - DEBUG - Set correlation ID to: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b
2025-06-29 02:12:59 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b in node_executor
2025-06-29 02:12:59 - AgentExecutor - DEBUG - Set correlation ID to: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b
2025-06-29 02:12:59 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b in agent_executor
2025-06-29 02:12:59 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-29 02:12:59 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-06-29 02:12:59 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-29 02:12:59 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b
2025-06-29 02:12:59 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b
2025-06-29 02:12:59 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-29 02:12:59 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-29 02:12:59 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-29 02:12:59 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-29 02:12:59 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-29 02:13:00 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b'
2025-06-29 02:13:00 - RedisManager - DEBUG - Set key 'workflow_state:154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b' with TTL of 600 seconds
2025-06-29 02:13:00 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:13:00 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-29 02:13:00 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-29 02:13:00 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-29 02:13:00 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-29 02:13:00 - StateManager - INFO - Terminated: False
2025-06-29 02:13:00 - StateManager - INFO - Pending transitions (0): []
2025-06-29 02:13:00 - StateManager - INFO - Waiting transitions (0): []
2025-06-29 02:13:00 - StateManager - INFO - Completed transitions (0): []
2025-06-29 02:13:00 - StateManager - INFO - Results stored for 0 transitions
2025-06-29 02:13:00 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-29 02:13:00 - StateManager - INFO - Workflow status: inactive
2025-06-29 02:13:00 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-29 02:13:00 - StateManager - INFO - Workflow status: inactive
2025-06-29 02:13:00 - StateManager - INFO - Workflow paused: False
2025-06-29 02:13:00 - StateManager - INFO - ==============================
2025-06-29 02:13:00 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-29 02:13:00 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:13:00 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-29 02:13:00 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-29 02:13:00 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-29 02:13:00 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-29 02:13:00 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-29 02:13:00 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-29 02:13:00 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-29 02:13:00 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-29 02:13:00 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-29 02:13:00 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-29 02:13:00 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-29 02:13:00 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:13:00 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-29 02:13:00 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-29 02:13:00 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-29 02:13:00 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-29 02:13:00 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-29 02:13:00 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-29 02:13:00 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-29 02:13:00 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-29 02:13:00 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-29 02:13:00 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-29 02:13:00 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-29 02:13:00 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-29 02:13:00 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-29 02:13:00 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-29 02:13:00 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-29 02:13:00 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-29 02:13:00 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-29 02:13:00 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-29 02:13:00 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-29 02:13:00 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-29 02:13:00 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-29 02:13:00 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-CombineTextComponent-*************']
2025-06-29 02:13:00 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-29 02:13:00 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain directly
2025-06-29 02:13:00 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-29 02:13:01 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-29 02:13:02 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-29 02:13:02 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:13:02 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-29 02:13:02 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-29 02:13:02 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-29 02:13:02 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-29 02:13:02 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-29 02:13:02 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:13:02 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-29 02:13:02 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration'}
2025-06-29 02:13:02 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-29 02:13:02 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 278069.100850458, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 02:13:02 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-29 02:13:02 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-CombineTextComponent-*************
2025-06-29 02:13:02 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-CombineTextComponent-*************
2025-06-29 02:13:02 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-29 02:13:02 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:13:02 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 2, 'workflow_status': 'running'}
2025-06-29 02:13:02 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-29 02:13:02 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-29 02:13:02 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-29 02:13:02 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-29 02:13:02 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-29 02:13:03 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-06-29 02:13:05 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b
2025-06-29 02:13:06 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-06-29 02:13:06 - StateManager - INFO - Using in-memory result for transition transition-LoopNode-*************.
2025-06-29 02:13:06 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-29 02:13:06 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 278069.100850458, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-29 02:13:06 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-29 02:13:06 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 278069.100850458, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-29 02:13:06 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-29 02:13:06 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-29 02:13:06 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-29 02:13:06 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-29 02:13:06 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-29 02:13:06 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-29 02:13:06 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 02:13:06 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 02:13:06 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 02:13:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:13:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 3, 'workflow_status': 'running'}
2025-06-29 02:13:06 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 16213b1b-e87b-4078-8f20-fbea2b22f123) using provided producer.
2025-06-29 02:13:06 - NodeExecutor - DEBUG - Added correlation_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b to payload
2025-06-29 02:13:06 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-29 02:13:06 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '16213b1b-e87b-4078-8f20-fbea2b22f123', 'correlation_id': '154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-29 02:13:06 - NodeExecutor - DEBUG - Request 16213b1b-e87b-4078-8f20-fbea2b22f123 sent successfully using provided producer.
2025-06-29 02:13:06 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 16213b1b-e87b-4078-8f20-fbea2b22f123...
2025-06-29 02:13:06 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1042, corr_id: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b
2025-06-29 02:13:07 - NodeExecutor - DEBUG - Result consumer received message: Offset=927
2025-06-29 02:13:07 - NodeExecutor - DEBUG - Received valid result for request_id 16213b1b-e87b-4078-8f20-fbea2b22f123
2025-06-29 02:13:07 - NodeExecutor - INFO - Result received for request 16213b1b-e87b-4078-8f20-fbea2b22f123.
2025-06-29 02:13:07 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-29 02:13:07 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:13:07 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 4, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 02:13:07 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751143387.420786}}
2025-06-29 02:13:07 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-29 02:13:08 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-29 02:13:08 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:13:08 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 02:13:08 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'transition-CombineTextComponent-*************', 'current_iteration'}
2025-06-29 02:13:08 - LoopExecutor - INFO - 🚨 notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-29 02:13:08 - TransitionHandler - ERROR - ❌ Error notifying loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************: object NoneType can't be used in 'await' expression
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:13:08 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 5.25 seconds
2025-06-29 02:13:08 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:13:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Completed transition in 5.25 seconds', 'message': 'Transition completed in 5.25 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 5, 'workflow_status': 'running'}
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-29 02:13:08 - LoopExecutor - DEBUG - 🔧 Transition transition-CombineTextComponent-************* execution completed, result type: <class 'list'>
2025-06-29 02:13:08 - LoopExecutor - DEBUG - 🔧 Processing result for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:13:08 - LoopExecutor - DEBUG - 🔗 Transition transition-CombineTextComponent-************* returned next transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:13:08 - LoopExecutor - DEBUG - 🔧 Extended next_transitions, now: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:13:08 - LoopExecutor - DEBUG - 🔗 Moving to next transitions in chain: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:13:08 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:13:08 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:13:08 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:13:08 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:13:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Starting execution of transition: transition-MCP_script-generation-mcp_script_generate-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-06-29 02:13:08 - TransitionHandler - EXECUTE - Transition 'transition-MCP_script-generation-mcp_script_generate-*************' (type=standard, execution_type=MCP)
2025-06-29 02:13:08 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: MCP
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: mcp
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for mcp node
2025-06-29 02:13:08 - StateManager - DEBUG - Retrieved result for transition transition-CombineTextComponent-************* from Redis
2025-06-29 02:13:08 - StateManager - DEBUG - Detected wrapped result structure for transition transition-CombineTextComponent-*************, extracting data
2025-06-29 02:13:08 - StateManager - DEBUG - Extracted double-nested result data for transition transition-CombineTextComponent-*************
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-CombineTextComponent-*************
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-CombineTextComponent-************* (total: 1)
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'result': {'result': 'blockchain and AI'}
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle result: blockchain and AI
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - Found result.result: blockchain and AI (type: <class 'str'>)
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - No handle matches found for 'result', treating result as single-value
2025-06-29 02:13:08 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-29 02:13:08 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'result': {'result': 'blockchain and AI'}
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle result: blockchain and AI
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - Found result.result: blockchain and AI (type: <class 'str'>)
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - No handle matches found for 'result', treating result as single-value
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - ✅ Handle mapping success: result → topic via path 'result': blockchain and AI
2025-06-29 02:13:08 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-29 02:13:08 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - Filtering out field 'topic' with null/empty value: None
2025-06-29 02:13:08 - WorkflowUtils - DEBUG - Filtering out field 'link' with null/empty value: None
2025-06-29 02:13:08 - WorkflowUtils - INFO - 🧹 Parameter filtering: 5 → 3 fields (2 null/empty fields removed)
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 📌 Added static parameter: script_type = TOPIC
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 📌 Added static parameter: keywords = 1 min marketing story
2025-06-29 02:13:08 - TransitionHandler - DEBUG - 📌 Added static parameter: video_type = SHORT
2025-06-29 02:13:08 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'topic': 'blockchain and AI', 'script_type': 'TOPIC', 'keywords': '1 min marketing story', 'video_type': 'SHORT'}
2025-06-29 02:13:08 - TransitionHandler - DEBUG - tool Parameters: {'topic': 'blockchain and AI', 'script_type': 'TOPIC', 'keywords': '1 min marketing story', 'video_type': 'SHORT'}
2025-06-29 02:13:08 - TransitionHandler - INFO - Invoking tool 'script_generate' (tool_id: 1) for node '748a8221-d7d9-4352-93ae-00700f4d28b1' in transition 'transition-MCP_script-generation-mcp_script_generate-*************' with parameters: {'topic': 'blockchain and AI', 'script_type': 'TOPIC', 'keywords': '1 min marketing story', 'video_type': 'SHORT'}
2025-06-29 02:13:08 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:13:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************', 'node_id': '748a8221-d7d9-4352-93ae-00700f4d28b1', 'tool_name': 'script_generate', 'message': 'Connecting to server', 'result': 'Connecting to server 748a8221-d7d9-4352-93ae-00700f4d28b1', 'status': 'connecting', 'sequence': 7, 'workflow_status': 'running'}
2025-06-29 02:13:08 - MCPToolExecutor - INFO - Executing tool 'script_generate' via Kafka (request_id: 3674285c-ed45-4c5c-aaee-2f59ef7b4ac4) with correlation_id: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, user_id: c1454e90-09ac-40f2-bde2-833387d7b645, mcp_id: 748a8221-d7d9-4352-93ae-00700f4d28b1 using provided producer.
2025-06-29 02:13:08 - MCPToolExecutor - DEBUG - Added correlation_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b to payload
2025-06-29 02:13:08 - MCPToolExecutor - DEBUG - Added user_id c1454e90-09ac-40f2-bde2-833387d7b645 to payload
2025-06-29 02:13:08 - MCPToolExecutor - DEBUG - Added mcp_id 748a8221-d7d9-4352-93ae-00700f4d28b1 to payload
2025-06-29 02:13:08 - MCPToolExecutor - DEBUG - Added transition_id transition-MCP_script-generation-mcp_script_generate-************* to payload
2025-06-29 02:13:08 - MCPToolExecutor - DEBUG - Sending request to topic 'mcp-execution-request': {'tool_name': 'script_generate', 'tool_parameters': {'topic': 'blockchain and AI', 'script_type': 'TOPIC', 'keywords': '1 min marketing story', 'video_type': 'SHORT'}, 'request_id': '3674285c-ed45-4c5c-aaee-2f59ef7b4ac4', 'correlation_id': '154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b', 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645', 'mcp_id': '748a8221-d7d9-4352-93ae-00700f4d28b1', 'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************'}
2025-06-29 02:13:08 - MCPToolExecutor - DEBUG - Request 3674285c-ed45-4c5c-aaee-2f59ef7b4ac4 sent successfully using provided producer.
2025-06-29 02:13:08 - MCPToolExecutor - DEBUG - Waiting indefinitely for result for request 3674285c-ed45-4c5c-aaee-2f59ef7b4ac4...
2025-06-29 02:13:22 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 02:13:23 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 02:13:23 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 02:13:23 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 02:14:01 - MCPToolExecutor - DEBUG - Result consumer received message: Offset=1014
2025-06-29 02:14:01 - MCPToolExecutor - DEBUG - Received valid result for request_id 3674285c-ed45-4c5c-aaee-2f59ef7b4ac4
2025-06-29 02:14:01 - MCPToolExecutor - INFO - Result received for request 3674285c-ed45-4c5c-aaee-2f59ef7b4ac4.
2025-06-29 02:14:01 - TransitionHandler - INFO - Execution result from MCP executor: [
  {
    "title": "blockchain and AI",
    "script": "\"Unlock BlockchainAI Secrets for Youth Marketing Mastery!\"\n\nWhoa, imagine blockchain\u2019s transparency teaming up with AI's insights\u2014it's reshaping marketing strategies and making user interactions way more personal. Personalized approaches now craft truly unique journeys, boosting ROI by 20%, says McKinsey. Picture a future where decisions empower users. Intrigued? Dive deeper into blockchainAI's potential\u2026\n\n\"Mind blown? Ready to level up?\""
  }
]
2025-06-29 02:14:01 - HelperFunctions - DEBUG - Property: title Value: blockchain and AI
2025-06-29 02:14:01 - HelperFunctions - DEBUG - Property 'title' found in schema description
2025-06-29 02:14:01 - HelperFunctions - DEBUG - Data type set to 'string' based on schema
2025-06-29 02:14:01 - HelperFunctions - DEBUG - Property: script Value: "Unlock BlockchainAI Secrets for Youth Marketing Mastery!"

Whoa, imagine blockchain’s transparency teaming up with AI's insights—it's reshaping marketing strategies and making user interactions way more personal. Personalized approaches now craft truly unique journeys, boosting ROI by 20%, says McKinsey. Picture a future where decisions empower users. Intrigued? Dive deeper into blockchainAI's potential…

"Mind blown? Ready to level up?"
2025-06-29 02:14:01 - HelperFunctions - DEBUG - Property 'script' found in schema description
2025-06-29 02:14:01 - HelperFunctions - DEBUG - Data type set to 'string' based on schema
2025-06-29 02:14:01 - TransitionHandler - DEBUG - Formatted result with semantic types: [{'data': 'blockchain and AI', 'data_type': 'string', 'property_name': 'title', 'semantic_type': 'string'}, {'data': '"Unlock BlockchainAI Secrets for Youth Marketing Mastery!"\n\nWhoa, imagine blockchain’s transparency teaming up with AI\'s insights—it\'s reshaping marketing strategies and making user interactions way more personal. Personalized approaches now craft truly unique journeys, boosting ROI by 20%, says McKinsey. Picture a future where decisions empower users. Intrigued? Dive deeper into blockchainAI\'s potential…\n\n"Mind blown? Ready to level up?"', 'data_type': 'string', 'property_name': 'script', 'semantic_type': 'string'}]
2025-06-29 02:14:01 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:01 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************', 'node_id': '748a8221-d7d9-4352-93ae-00700f4d28b1', 'tool_name': 'script_generate', 'message': 'Transition Result received.', 'result': [{'data': 'blockchain and AI', 'data_type': 'string', 'property_name': 'title', 'semantic_type': 'string'}, {'data': '"Unlock BlockchainAI Secrets for Youth Marketing Mastery!"\n\nWhoa, imagine blockchain’s transparency teaming up with AI\'s insights—it\'s reshaping marketing strategies and making user interactions way more personal. Personalized approaches now craft truly unique journeys, boosting ROI by 20%, says McKinsey. Picture a future where decisions empower users. Intrigued? Dive deeper into blockchainAI\'s potential…\n\n"Mind blown? Ready to level up?"', 'data_type': 'string', 'property_name': 'script', 'semantic_type': 'string'}], 'status': 'completed', 'sequence': 8, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 02:14:01 - StateManager - DEBUG - Stored result for transition transition-MCP_script-generation-mcp_script_generate-************* in memory: {'script_generate': {'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************', 'node_id': '748a8221-d7d9-4352-93ae-00700f4d28b1', 'tool_name': 'script_generate', 'result': {'result': [{'title': 'blockchain and AI', 'script': '"Unlock BlockchainAI Secrets for Youth Marketing Mastery!"\n\nWhoa, imagine blockchain’s transparency teaming up with AI\'s insights—it\'s reshaping marketing strategies and making user interactions way more personal. Personalized approaches now craft truly unique journeys, boosting ROI by 20%, says McKinsey. Picture a future where decisions empower users. Intrigued? Dive deeper into blockchainAI\'s potential…\n\n"Mind blown? Ready to level up?"'}]}, 'status': 'completed', 'timestamp': 1751143441.345375}}
2025-06-29 02:14:02 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MCP_script-generation-mcp_script_generate-*************'
2025-06-29 02:14:02 - RedisManager - DEBUG - Set key 'result:transition-MCP_script-generation-mcp_script_generate-*************' with TTL of 300 seconds
2025-06-29 02:14:02 - StateManager - DEBUG - Stored result for transition transition-MCP_script-generation-mcp_script_generate-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:14:02 - StateManager - INFO - Marked transition transition-MCP_script-generation-mcp_script_generate-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 02:14:02 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'transition-CombineTextComponent-*************', 'transition-MCP_script-generation-mcp_script_generate-*************', 'current_iteration'}
2025-06-29 02:14:02 - LoopExecutor - INFO - 🚨 notify_transition_completion called for transition transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:14:02 - TransitionHandler - ERROR - ❌ Error notifying loop executor transition-LoopNode-************* about transition transition-MCP_script-generation-mcp_script_generate-*************: object NoneType can't be used in 'await' expression
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'list'>
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'list'>
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MCP_script-generation-mcp_script_generate-*************:
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MCP_script-generation-mcp_script_generate-*************, returning empty list
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:02 - TransitionHandler - INFO - Completed transition transition-MCP_script-generation-mcp_script_generate-************* in 54.30 seconds
2025-06-29 02:14:02 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:02 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Completed transition in 54.30 seconds', 'message': 'Transition completed in 54.30 seconds', 'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************', 'status': 'time_logged', 'sequence': 9, 'workflow_status': 'running'}
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-MCP_script-generation-mcp_script_generate-*************: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-29 02:14:02 - LoopExecutor - DEBUG - 🔧 Transition transition-MCP_script-generation-mcp_script_generate-************* execution completed, result type: <class 'list'>
2025-06-29 02:14:02 - LoopExecutor - DEBUG - 🔧 Processing result for transition-MCP_script-generation-mcp_script_generate-*************: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:02 - LoopExecutor - DEBUG - 🔗 Transition transition-MCP_script-generation-mcp_script_generate-************* returned next transitions: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:02 - LoopExecutor - DEBUG - 🔧 Extended next_transitions, now: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:02 - LoopExecutor - DEBUG - 🔗 Moving to next transitions in chain: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:02 - LoopExecutor - DEBUG - 🏁 Found exit transitions to execute: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:02 - LoopExecutor - DEBUG - 🚀 Executing exit transition: transition-CombineTextComponent-*************
2025-06-29 02:14:02 - LoopExecutor - DEBUG - 🔧 About to execute exit transition transition-CombineTextComponent-*************
2025-06-29 02:14:02 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-29 02:14:02 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:02 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 10, 'workflow_status': 'running'}
2025-06-29 02:14:02 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-29 02:14:02 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-29 02:14:02 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-29 02:14:03 - StateManager - DEBUG - Retrieved result for transition transition-MCP_script-generation-mcp_script_generate-************* from Redis
2025-06-29 02:14:03 - StateManager - DEBUG - Detected wrapped result structure for transition transition-MCP_script-generation-mcp_script_generate-*************, extracting data
2025-06-29 02:14:03 - StateManager - DEBUG - Extracted double-nested result data for transition transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:14:03 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-MCP_script-generation-mcp_script_generate-************* (total: 1)
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'title': {'result': [{'title': 'blockchain and AI', 'script': '"Unlock BlockchainAI Secrets for Youth Marketing Mastery!"\n\nWhoa, imagine blockchain’s transparency teaming up with AI\'s insights—it\'s reshaping marketing strategies and making user interactions way more personal. Personalized approaches now craft truly unique journeys, boosting ROI by 20%, says McKinsey. Picture a future where decisions empower users. Intrigued? Dive deeper into blockchainAI\'s potential…\n\n"Mind blown? Ready to level up?"'}]}
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle title: {'title': 'blockchain and AI', 'script': '"Unlock BlockchainAI Secrets for Youth Marketing Mastery!"\n\nWhoa, imagine blockchain’s transparency teaming up with AI\'s insights—it\'s reshaping marketing strategies and making user interactions way more personal. Personalized approaches now craft truly unique journeys, boosting ROI by 20%, says McKinsey. Picture a future where decisions empower users. Intrigued? Dive deeper into blockchainAI\'s potential…\n\n"Mind blown? Ready to level up?"'}
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Found result.result: {'title': 'blockchain and AI', 'script': '"Unlock BlockchainAI Secrets for Youth Marketing Mastery!"\n\nWhoa, imagine blockchain’s transparency teaming up with AI\'s insights—it\'s reshaping marketing strategies and making user interactions way more personal. Personalized approaches now craft truly unique journeys, boosting ROI by 20%, says McKinsey. Picture a future where decisions empower users. Intrigued? Dive deeper into blockchainAI\'s potential…\n\n"Mind blown? Ready to level up?"'} (type: <class 'dict'>)
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Found handle 'title' directly in dict
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Successfully extracted handle 'title' with path 'result.title': blockchain and AI
2025-06-29 02:14:03 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-29 02:14:03 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'title': {'result': [{'title': 'blockchain and AI', 'script': '"Unlock BlockchainAI Secrets for Youth Marketing Mastery!"\n\nWhoa, imagine blockchain’s transparency teaming up with AI\'s insights—it\'s reshaping marketing strategies and making user interactions way more personal. Personalized approaches now craft truly unique journeys, boosting ROI by 20%, says McKinsey. Picture a future where decisions empower users. Intrigued? Dive deeper into blockchainAI\'s potential…\n\n"Mind blown? Ready to level up?"'}]}
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle title: {'title': 'blockchain and AI', 'script': '"Unlock BlockchainAI Secrets for Youth Marketing Mastery!"\n\nWhoa, imagine blockchain’s transparency teaming up with AI\'s insights—it\'s reshaping marketing strategies and making user interactions way more personal. Personalized approaches now craft truly unique journeys, boosting ROI by 20%, says McKinsey. Picture a future where decisions empower users. Intrigued? Dive deeper into blockchainAI\'s potential…\n\n"Mind blown? Ready to level up?"'}
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Found result.result: {'title': 'blockchain and AI', 'script': '"Unlock BlockchainAI Secrets for Youth Marketing Mastery!"\n\nWhoa, imagine blockchain’s transparency teaming up with AI\'s insights—it\'s reshaping marketing strategies and making user interactions way more personal. Personalized approaches now craft truly unique journeys, boosting ROI by 20%, says McKinsey. Picture a future where decisions empower users. Intrigued? Dive deeper into blockchainAI\'s potential…\n\n"Mind blown? Ready to level up?"'} (type: <class 'dict'>)
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Found handle 'title' directly in dict
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Successfully extracted handle 'title' with path 'result.title': blockchain and AI
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - ✅ Handle mapping success: title → main_input via path 'result.title': blockchain and AI
2025-06-29 02:14:03 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-29 02:14:03 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Filtering out field 'num_additional_inputs' with null/empty value: None
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: None
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: None
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: None
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: None
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: None
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: None
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: None
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: None
2025-06-29 02:14:03 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: None
2025-06-29 02:14:03 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 0 fields (13 null/empty fields removed)
2025-06-29 02:14:03 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain and AI'}
2025-06-29 02:14:03 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain and AI'}
2025-06-29 02:14:03 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain and AI'}
2025-06-29 02:14:03 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:03 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 11, 'workflow_status': 'running'}
2025-06-29 02:14:03 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: c60ff9a8-12e1-4fac-8308-db7fd1ec1d81) using provided producer.
2025-06-29 02:14:03 - NodeExecutor - DEBUG - Added correlation_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b to payload
2025-06-29 02:14:03 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-29 02:14:03 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain and AI'}, 'request_id': 'c60ff9a8-12e1-4fac-8308-db7fd1ec1d81', 'correlation_id': '154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-29 02:14:03 - NodeExecutor - DEBUG - Request c60ff9a8-12e1-4fac-8308-db7fd1ec1d81 sent successfully using provided producer.
2025-06-29 02:14:03 - NodeExecutor - DEBUG - Waiting indefinitely for result for request c60ff9a8-12e1-4fac-8308-db7fd1ec1d81...
2025-06-29 02:14:03 - NodeExecutor - DEBUG - Result consumer received message: Offset=928
2025-06-29 02:14:03 - NodeExecutor - DEBUG - Received valid result for request_id c60ff9a8-12e1-4fac-8308-db7fd1ec1d81
2025-06-29 02:14:03 - NodeExecutor - INFO - Result received for request c60ff9a8-12e1-4fac-8308-db7fd1ec1d81.
2025-06-29 02:14:03 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-29 02:14:03 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:03 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 12, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 02:14:03 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751143443.916734}}
2025-06-29 02:14:04 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-29 02:14:04 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-29 02:14:04 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:14:04 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 02:14:04 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'transition-MCP_script-generation-mcp_script_generate-*************', 'transition-CombineTextComponent-*************', 'current_iteration', 'transition-CombineTextComponent-*************'}
2025-06-29 02:14:04 - LoopExecutor - INFO - 🚨 notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-29 02:14:04 - TransitionHandler - ERROR - ❌ Error notifying loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************: object NoneType can't be used in 'await' expression
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 0
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: []
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔗 Final next_transitions: []
2025-06-29 02:14:04 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.29 seconds
2025-06-29 02:14:04 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:04 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Completed transition in 2.29 seconds', 'message': 'Transition completed in 2.29 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 13, 'workflow_status': 'running'}
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: []
2025-06-29 02:14:04 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 0
2025-06-29 02:14:04 - LoopExecutor - DEBUG - 🔧 Exit transition transition-CombineTextComponent-************* execution completed, result type: <class 'list'>
2025-06-29 02:14:04 - LoopExecutor - DEBUG - ✅ Exit transition transition-CombineTextComponent-************* completed with result: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751143443.916734}}
2025-06-29 02:14:04 - LoopExecutor - DEBUG - 🎯 Extracted final result from exit transition: blockchain and AI
2025-06-29 02:14:04 - LoopExecutor - DEBUG - 🔚 All exit transitions executed, chain complete
2025-06-29 02:14:04 - LoopExecutor - DEBUG - 🔚 Chain execution completed, final_result: blockchain and AI
2025-06-29 02:14:04 - LoopExecutor - INFO - ✅ Iteration 1 completed successfully
2025-06-29 02:14:04 - LoopExecutor - INFO - 🔄 Starting iteration 2/3
2025-06-29 02:14:04 - LoopExecutor - DEBUG - 🔄 Executing iteration 2 chain directly
2025-06-29 02:14:04 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in memory: automation
2025-06-29 02:14:05 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_1
2025-06-29 02:14:05 - RedisManager - DEBUG - Set key 'result:loop_iteration_1' with TTL of 900 seconds
2025-06-29 02:14:05 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:14:05 - StateManager - INFO - Marked transition loop_iteration_1 as completed (was_pending=False, was_waiting=False)
2025-06-29 02:14:05 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'loop_iteration_1', 'transition-MCP_script-generation-mcp_script_generate-*************', 'transition-CombineTextComponent-*************', 'current_iteration', 'transition-CombineTextComponent-*************'}
2025-06-29 02:14:05 - StateManager - DEBUG - Stored result for transition current_iteration in memory: automation
2025-06-29 02:14:05 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-29 02:14:06 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-29 02:14:06 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:14:06 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-29 02:14:06 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'loop_iteration_1', 'transition-MCP_script-generation-mcp_script_generate-*************', 'transition-CombineTextComponent-*************', 'current_iteration', 'transition-CombineTextComponent-*************'}
2025-06-29 02:14:06 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-29 02:14:06 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 'automation', 'iteration_index': 1, 'iteration_metadata': {'timestamp': 278132.478471375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 02:14:06 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_1
2025-06-29 02:14:06 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-CombineTextComponent-*************
2025-06-29 02:14:06 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-CombineTextComponent-*************
2025-06-29 02:14:06 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-29 02:14:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 14, 'workflow_status': 'running'}
2025-06-29 02:14:06 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-29 02:14:06 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-29 02:14:06 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-29 02:14:06 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-29 02:14:06 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-29 02:14:07 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-06-29 02:14:09 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b
2025-06-29 02:14:09 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-06-29 02:14:09 - StateManager - INFO - Using in-memory result for transition transition-LoopNode-*************.
2025-06-29 02:14:09 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-29 02:14:09 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'automation', 'iteration_index': 1, 'iteration_metadata': {'timestamp': 278132.478471375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-29 02:14:09 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-29 02:14:09 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'automation', 'iteration_index': 1, 'iteration_metadata': {'timestamp': 278132.478471375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': automation
2025-06-29 02:14:09 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-29 02:14:09 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-29 02:14:09 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-29 02:14:09 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-29 02:14:09 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-29 02:14:09 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-29 02:14:09 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'automation', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 02:14:09 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'automation', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 02:14:09 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'automation', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 02:14:09 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:09 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 15, 'workflow_status': 'running'}
2025-06-29 02:14:09 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 9216b7a0-b094-41a9-9e3a-a7d50f5e36a8) using provided producer.
2025-06-29 02:14:09 - NodeExecutor - DEBUG - Added correlation_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b to payload
2025-06-29 02:14:09 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-29 02:14:09 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'automation', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '9216b7a0-b094-41a9-9e3a-a7d50f5e36a8', 'correlation_id': '154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-29 02:14:09 - NodeExecutor - DEBUG - Request 9216b7a0-b094-41a9-9e3a-a7d50f5e36a8 sent successfully using provided producer.
2025-06-29 02:14:09 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 9216b7a0-b094-41a9-9e3a-a7d50f5e36a8...
2025-06-29 02:14:10 - NodeExecutor - DEBUG - Result consumer received message: Offset=929
2025-06-29 02:14:10 - NodeExecutor - DEBUG - Received valid result for request_id 9216b7a0-b094-41a9-9e3a-a7d50f5e36a8
2025-06-29 02:14:10 - NodeExecutor - INFO - Result received for request 9216b7a0-b094-41a9-9e3a-a7d50f5e36a8.
2025-06-29 02:14:10 - TransitionHandler - INFO - Execution result from Components executor: "automation and AI"
2025-06-29 02:14:10 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:10 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'automation and AI', 'status': 'completed', 'sequence': 16, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 02:14:10 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'automation and AI'}, 'status': 'completed', 'timestamp': 1751143450.017747}}
2025-06-29 02:14:10 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-29 02:14:10 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-29 02:14:10 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:14:10 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 02:14:10 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'loop_iteration_1', 'transition-MCP_script-generation-mcp_script_generate-*************', 'transition-CombineTextComponent-*************', 'current_iteration', 'transition-CombineTextComponent-*************'}
2025-06-29 02:14:10 - LoopExecutor - INFO - 🚨 notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-29 02:14:10 - TransitionHandler - ERROR - ❌ Error notifying loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************: object NoneType can't be used in 'await' expression
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:14:10 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 4.46 seconds
2025-06-29 02:14:10 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:10 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Completed transition in 4.46 seconds', 'message': 'Transition completed in 4.46 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 17, 'workflow_status': 'running'}
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-29 02:14:10 - LoopExecutor - DEBUG - 🔧 Transition transition-CombineTextComponent-************* execution completed, result type: <class 'list'>
2025-06-29 02:14:10 - LoopExecutor - DEBUG - 🔧 Processing result for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:14:10 - LoopExecutor - DEBUG - 🔗 Transition transition-CombineTextComponent-************* returned next transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:14:10 - LoopExecutor - DEBUG - 🔧 Extended next_transitions, now: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:14:10 - LoopExecutor - DEBUG - 🔗 Moving to next transitions in chain: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:14:10 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:14:10 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:14:10 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:14:10 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:10 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Starting execution of transition: transition-MCP_script-generation-mcp_script_generate-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************', 'status': 'started', 'sequence': 18, 'workflow_status': 'running'}
2025-06-29 02:14:10 - TransitionHandler - EXECUTE - Transition 'transition-MCP_script-generation-mcp_script_generate-*************' (type=standard, execution_type=MCP)
2025-06-29 02:14:10 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: MCP
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: mcp
2025-06-29 02:14:10 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for mcp node
2025-06-29 02:14:11 - StateManager - DEBUG - Retrieved result for transition transition-CombineTextComponent-************* from Redis
2025-06-29 02:14:11 - StateManager - DEBUG - Detected wrapped result structure for transition transition-CombineTextComponent-*************, extracting data
2025-06-29 02:14:11 - StateManager - DEBUG - Extracted double-nested result data for transition transition-CombineTextComponent-*************
2025-06-29 02:14:11 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-CombineTextComponent-*************
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-CombineTextComponent-************* (total: 1)
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'result': {'result': 'automation and AI'}
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle result: automation and AI
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - Found result.result: automation and AI (type: <class 'str'>)
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - No handle matches found for 'result', treating result as single-value
2025-06-29 02:14:11 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-29 02:14:11 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'result': {'result': 'automation and AI'}
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle result: automation and AI
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - Found result.result: automation and AI (type: <class 'str'>)
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - No handle matches found for 'result', treating result as single-value
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - ✅ Handle mapping success: result → topic via path 'result': automation and AI
2025-06-29 02:14:11 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-29 02:14:11 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - Filtering out field 'topic' with null/empty value: None
2025-06-29 02:14:11 - WorkflowUtils - DEBUG - Filtering out field 'link' with null/empty value: None
2025-06-29 02:14:11 - WorkflowUtils - INFO - 🧹 Parameter filtering: 5 → 3 fields (2 null/empty fields removed)
2025-06-29 02:14:11 - TransitionHandler - DEBUG - 📌 Added static parameter: script_type = TOPIC
2025-06-29 02:14:11 - TransitionHandler - DEBUG - 📌 Added static parameter: keywords = 1 min marketing story
2025-06-29 02:14:11 - TransitionHandler - DEBUG - 📌 Added static parameter: video_type = SHORT
2025-06-29 02:14:11 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'topic': 'automation and AI', 'script_type': 'TOPIC', 'keywords': '1 min marketing story', 'video_type': 'SHORT'}
2025-06-29 02:14:11 - TransitionHandler - DEBUG - tool Parameters: {'topic': 'automation and AI', 'script_type': 'TOPIC', 'keywords': '1 min marketing story', 'video_type': 'SHORT'}
2025-06-29 02:14:11 - TransitionHandler - INFO - Invoking tool 'script_generate' (tool_id: 1) for node '748a8221-d7d9-4352-93ae-00700f4d28b1' in transition 'transition-MCP_script-generation-mcp_script_generate-*************' with parameters: {'topic': 'automation and AI', 'script_type': 'TOPIC', 'keywords': '1 min marketing story', 'video_type': 'SHORT'}
2025-06-29 02:14:11 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:11 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************', 'node_id': '748a8221-d7d9-4352-93ae-00700f4d28b1', 'tool_name': 'script_generate', 'message': 'Connecting to server', 'result': 'Connecting to server 748a8221-d7d9-4352-93ae-00700f4d28b1', 'status': 'connecting', 'sequence': 19, 'workflow_status': 'running'}
2025-06-29 02:14:11 - MCPToolExecutor - INFO - Executing tool 'script_generate' via Kafka (request_id: 4d3c2906-1351-420f-8c5d-f6d84f68d00a) with correlation_id: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, user_id: c1454e90-09ac-40f2-bde2-833387d7b645, mcp_id: 748a8221-d7d9-4352-93ae-00700f4d28b1 using provided producer.
2025-06-29 02:14:11 - MCPToolExecutor - DEBUG - Added correlation_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b to payload
2025-06-29 02:14:11 - MCPToolExecutor - DEBUG - Added user_id c1454e90-09ac-40f2-bde2-833387d7b645 to payload
2025-06-29 02:14:11 - MCPToolExecutor - DEBUG - Added mcp_id 748a8221-d7d9-4352-93ae-00700f4d28b1 to payload
2025-06-29 02:14:11 - MCPToolExecutor - DEBUG - Added transition_id transition-MCP_script-generation-mcp_script_generate-************* to payload
2025-06-29 02:14:11 - MCPToolExecutor - DEBUG - Sending request to topic 'mcp-execution-request': {'tool_name': 'script_generate', 'tool_parameters': {'topic': 'automation and AI', 'script_type': 'TOPIC', 'keywords': '1 min marketing story', 'video_type': 'SHORT'}, 'request_id': '4d3c2906-1351-420f-8c5d-f6d84f68d00a', 'correlation_id': '154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b', 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645', 'mcp_id': '748a8221-d7d9-4352-93ae-00700f4d28b1', 'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************'}
2025-06-29 02:14:11 - MCPToolExecutor - DEBUG - Request 4d3c2906-1351-420f-8c5d-f6d84f68d00a sent successfully using provided producer.
2025-06-29 02:14:11 - MCPToolExecutor - DEBUG - Waiting indefinitely for result for request 4d3c2906-1351-420f-8c5d-f6d84f68d00a...
2025-06-29 02:14:22 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 02:14:23 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 02:14:23 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 02:14:23 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 02:14:55 - MCPToolExecutor - DEBUG - Result consumer received message: Offset=1015
2025-06-29 02:14:55 - MCPToolExecutor - DEBUG - Received valid result for request_id 4d3c2906-1351-420f-8c5d-f6d84f68d00a
2025-06-29 02:14:55 - MCPToolExecutor - INFO - Result received for request 4d3c2906-1351-420f-8c5d-f6d84f68d00a.
2025-06-29 02:14:55 - TransitionHandler - INFO - Execution result from MCP executor: [
  {
    "title": "automation and AI",
    "script": "\"Unlock AI's Secrets: Elevate Your Marketing in Seconds! Discover how AI and automation boost marketing efficiency and personalization. AI is really upping ROI, saving time, and engaging customers instantly! Want to dive deeper into this game-changer? Comment below for more AI insights! Ready to transform? Subscribe for unbeatable marketing tips!\""
  }
]
2025-06-29 02:14:55 - HelperFunctions - DEBUG - Property: title Value: automation and AI
2025-06-29 02:14:55 - HelperFunctions - DEBUG - Property 'title' found in schema description
2025-06-29 02:14:55 - HelperFunctions - DEBUG - Data type set to 'string' based on schema
2025-06-29 02:14:55 - HelperFunctions - DEBUG - Property: script Value: "Unlock AI's Secrets: Elevate Your Marketing in Seconds! Discover how AI and automation boost marketing efficiency and personalization. AI is really upping ROI, saving time, and engaging customers instantly! Want to dive deeper into this game-changer? Comment below for more AI insights! Ready to transform? Subscribe for unbeatable marketing tips!"
2025-06-29 02:14:55 - HelperFunctions - DEBUG - Property 'script' found in schema description
2025-06-29 02:14:55 - HelperFunctions - DEBUG - Data type set to 'string' based on schema
2025-06-29 02:14:55 - TransitionHandler - DEBUG - Formatted result with semantic types: [{'data': 'automation and AI', 'data_type': 'string', 'property_name': 'title', 'semantic_type': 'string'}, {'data': '"Unlock AI\'s Secrets: Elevate Your Marketing in Seconds! Discover how AI and automation boost marketing efficiency and personalization. AI is really upping ROI, saving time, and engaging customers instantly! Want to dive deeper into this game-changer? Comment below for more AI insights! Ready to transform? Subscribe for unbeatable marketing tips!"', 'data_type': 'string', 'property_name': 'script', 'semantic_type': 'string'}]
2025-06-29 02:14:55 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 20, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:55 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************', 'node_id': '748a8221-d7d9-4352-93ae-00700f4d28b1', 'tool_name': 'script_generate', 'message': 'Transition Result received.', 'result': [{'data': 'automation and AI', 'data_type': 'string', 'property_name': 'title', 'semantic_type': 'string'}, {'data': '"Unlock AI\'s Secrets: Elevate Your Marketing in Seconds! Discover how AI and automation boost marketing efficiency and personalization. AI is really upping ROI, saving time, and engaging customers instantly! Want to dive deeper into this game-changer? Comment below for more AI insights! Ready to transform? Subscribe for unbeatable marketing tips!"', 'data_type': 'string', 'property_name': 'script', 'semantic_type': 'string'}], 'status': 'completed', 'sequence': 20, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 02:14:55 - StateManager - DEBUG - Stored result for transition transition-MCP_script-generation-mcp_script_generate-************* in memory: {'script_generate': {'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************', 'node_id': '748a8221-d7d9-4352-93ae-00700f4d28b1', 'tool_name': 'script_generate', 'result': {'result': [{'title': 'automation and AI', 'script': '"Unlock AI\'s Secrets: Elevate Your Marketing in Seconds! Discover how AI and automation boost marketing efficiency and personalization. AI is really upping ROI, saving time, and engaging customers instantly! Want to dive deeper into this game-changer? Comment below for more AI insights! Ready to transform? Subscribe for unbeatable marketing tips!"'}]}, 'status': 'completed', 'timestamp': 1751143495.543998}}
2025-06-29 02:14:56 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MCP_script-generation-mcp_script_generate-*************'
2025-06-29 02:14:56 - RedisManager - DEBUG - Set key 'result:transition-MCP_script-generation-mcp_script_generate-*************' with TTL of 300 seconds
2025-06-29 02:14:56 - StateManager - DEBUG - Stored result for transition transition-MCP_script-generation-mcp_script_generate-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:14:56 - StateManager - INFO - Marked transition transition-MCP_script-generation-mcp_script_generate-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 02:14:56 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'loop_iteration_1', 'transition-MCP_script-generation-mcp_script_generate-*************', 'transition-CombineTextComponent-*************', 'current_iteration', 'transition-CombineTextComponent-*************'}
2025-06-29 02:14:56 - LoopExecutor - INFO - 🚨 notify_transition_completion called for transition transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:14:56 - TransitionHandler - ERROR - ❌ Error notifying loop executor transition-LoopNode-************* about transition transition-MCP_script-generation-mcp_script_generate-*************: object NoneType can't be used in 'await' expression
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'list'>
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'list'>
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MCP_script-generation-mcp_script_generate-*************:
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MCP_script-generation-mcp_script_generate-*************, returning empty list
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:56 - TransitionHandler - INFO - Completed transition transition-MCP_script-generation-mcp_script_generate-************* in 46.00 seconds
2025-06-29 02:14:56 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 21, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:56 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Completed transition in 46.00 seconds', 'message': 'Transition completed in 46.00 seconds', 'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************', 'status': 'time_logged', 'sequence': 21, 'workflow_status': 'running'}
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-MCP_script-generation-mcp_script_generate-*************: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-29 02:14:56 - LoopExecutor - DEBUG - 🔧 Transition transition-MCP_script-generation-mcp_script_generate-************* execution completed, result type: <class 'list'>
2025-06-29 02:14:56 - LoopExecutor - DEBUG - 🔧 Processing result for transition-MCP_script-generation-mcp_script_generate-*************: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:56 - LoopExecutor - DEBUG - 🔗 Transition transition-MCP_script-generation-mcp_script_generate-************* returned next transitions: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:56 - LoopExecutor - DEBUG - 🔧 Extended next_transitions, now: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:56 - LoopExecutor - DEBUG - 🔗 Moving to next transitions in chain: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:56 - LoopExecutor - DEBUG - 🏁 Found exit transitions to execute: ['transition-CombineTextComponent-*************']
2025-06-29 02:14:56 - LoopExecutor - DEBUG - 🚀 Executing exit transition: transition-CombineTextComponent-*************
2025-06-29 02:14:56 - LoopExecutor - DEBUG - 🔧 About to execute exit transition transition-CombineTextComponent-*************
2025-06-29 02:14:56 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-29 02:14:56 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 22, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:56 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 22, 'workflow_status': 'running'}
2025-06-29 02:14:56 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-29 02:14:56 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-29 02:14:56 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-29 02:14:57 - StateManager - DEBUG - Retrieved result for transition transition-MCP_script-generation-mcp_script_generate-************* from Redis
2025-06-29 02:14:57 - StateManager - DEBUG - Detected wrapped result structure for transition transition-MCP_script-generation-mcp_script_generate-*************, extracting data
2025-06-29 02:14:57 - StateManager - DEBUG - Extracted double-nested result data for transition transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:14:57 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-MCP_script-generation-mcp_script_generate-************* (total: 1)
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'title': {'result': [{'title': 'automation and AI', 'script': '"Unlock AI\'s Secrets: Elevate Your Marketing in Seconds! Discover how AI and automation boost marketing efficiency and personalization. AI is really upping ROI, saving time, and engaging customers instantly! Want to dive deeper into this game-changer? Comment below for more AI insights! Ready to transform? Subscribe for unbeatable marketing tips!"'}]}
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle title: {'title': 'automation and AI', 'script': '"Unlock AI\'s Secrets: Elevate Your Marketing in Seconds! Discover how AI and automation boost marketing efficiency and personalization. AI is really upping ROI, saving time, and engaging customers instantly! Want to dive deeper into this game-changer? Comment below for more AI insights! Ready to transform? Subscribe for unbeatable marketing tips!"'}
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Found result.result: {'title': 'automation and AI', 'script': '"Unlock AI\'s Secrets: Elevate Your Marketing in Seconds! Discover how AI and automation boost marketing efficiency and personalization. AI is really upping ROI, saving time, and engaging customers instantly! Want to dive deeper into this game-changer? Comment below for more AI insights! Ready to transform? Subscribe for unbeatable marketing tips!"'} (type: <class 'dict'>)
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Found handle 'title' directly in dict
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Successfully extracted handle 'title' with path 'result.title': automation and AI
2025-06-29 02:14:57 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-29 02:14:57 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'title': {'result': [{'title': 'automation and AI', 'script': '"Unlock AI\'s Secrets: Elevate Your Marketing in Seconds! Discover how AI and automation boost marketing efficiency and personalization. AI is really upping ROI, saving time, and engaging customers instantly! Want to dive deeper into this game-changer? Comment below for more AI insights! Ready to transform? Subscribe for unbeatable marketing tips!"'}]}
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle title: {'title': 'automation and AI', 'script': '"Unlock AI\'s Secrets: Elevate Your Marketing in Seconds! Discover how AI and automation boost marketing efficiency and personalization. AI is really upping ROI, saving time, and engaging customers instantly! Want to dive deeper into this game-changer? Comment below for more AI insights! Ready to transform? Subscribe for unbeatable marketing tips!"'}
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Found result.result: {'title': 'automation and AI', 'script': '"Unlock AI\'s Secrets: Elevate Your Marketing in Seconds! Discover how AI and automation boost marketing efficiency and personalization. AI is really upping ROI, saving time, and engaging customers instantly! Want to dive deeper into this game-changer? Comment below for more AI insights! Ready to transform? Subscribe for unbeatable marketing tips!"'} (type: <class 'dict'>)
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Found handle 'title' directly in dict
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Successfully extracted handle 'title' with path 'result.title': automation and AI
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - ✅ Handle mapping success: title → main_input via path 'result.title': automation and AI
2025-06-29 02:14:57 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-29 02:14:57 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Filtering out field 'num_additional_inputs' with null/empty value: None
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: None
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: None
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: None
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: None
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: None
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: None
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: None
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: None
2025-06-29 02:14:57 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: None
2025-06-29 02:14:57 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 0 fields (13 null/empty fields removed)
2025-06-29 02:14:57 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'automation and AI'}
2025-06-29 02:14:57 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'automation and AI'}
2025-06-29 02:14:57 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'automation and AI'}
2025-06-29 02:14:57 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 23, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:57 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 23, 'workflow_status': 'running'}
2025-06-29 02:14:57 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 01d2ebd7-60d9-4152-a4aa-b49f4a2dd2a1) using provided producer.
2025-06-29 02:14:57 - NodeExecutor - DEBUG - Added correlation_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b to payload
2025-06-29 02:14:57 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-29 02:14:57 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'automation and AI'}, 'request_id': '01d2ebd7-60d9-4152-a4aa-b49f4a2dd2a1', 'correlation_id': '154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-29 02:14:57 - NodeExecutor - DEBUG - Request 01d2ebd7-60d9-4152-a4aa-b49f4a2dd2a1 sent successfully using provided producer.
2025-06-29 02:14:57 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 01d2ebd7-60d9-4152-a4aa-b49f4a2dd2a1...
2025-06-29 02:14:58 - NodeExecutor - DEBUG - Result consumer received message: Offset=930
2025-06-29 02:14:58 - NodeExecutor - DEBUG - Received valid result for request_id 01d2ebd7-60d9-4152-a4aa-b49f4a2dd2a1
2025-06-29 02:14:58 - NodeExecutor - INFO - Result received for request 01d2ebd7-60d9-4152-a4aa-b49f4a2dd2a1.
2025-06-29 02:14:58 - TransitionHandler - INFO - Execution result from Components executor: "automation and AI"
2025-06-29 02:14:58 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 24, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:58 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'automation and AI', 'status': 'completed', 'sequence': 24, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 02:14:58 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'automation and AI'}, 'status': 'completed', 'timestamp': 1751143498.3413}}
2025-06-29 02:14:58 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-29 02:14:59 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-29 02:14:59 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:14:59 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 02:14:59 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'loop_iteration_1', 'transition-MCP_script-generation-mcp_script_generate-*************', 'transition-CombineTextComponent-*************', 'current_iteration', 'transition-CombineTextComponent-*************'}
2025-06-29 02:14:59 - LoopExecutor - INFO - 🚨 notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-29 02:14:59 - TransitionHandler - ERROR - ❌ Error notifying loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************: object NoneType can't be used in 'await' expression
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 0
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: []
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔗 Final next_transitions: []
2025-06-29 02:14:59 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.33 seconds
2025-06-29 02:14:59 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 25, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:14:59 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Completed transition in 2.33 seconds', 'message': 'Transition completed in 2.33 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 25, 'workflow_status': 'running'}
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: []
2025-06-29 02:14:59 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 0
2025-06-29 02:14:59 - LoopExecutor - DEBUG - 🔧 Exit transition transition-CombineTextComponent-************* execution completed, result type: <class 'list'>
2025-06-29 02:14:59 - LoopExecutor - DEBUG - ✅ Exit transition transition-CombineTextComponent-************* completed with result: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'automation and AI'}, 'status': 'completed', 'timestamp': 1751143498.3413}}
2025-06-29 02:14:59 - LoopExecutor - DEBUG - 🎯 Extracted final result from exit transition: automation and AI
2025-06-29 02:14:59 - LoopExecutor - DEBUG - 🔚 All exit transitions executed, chain complete
2025-06-29 02:14:59 - LoopExecutor - DEBUG - 🔚 Chain execution completed, final_result: automation and AI
2025-06-29 02:14:59 - LoopExecutor - INFO - ✅ Iteration 2 completed successfully
2025-06-29 02:14:59 - LoopExecutor - INFO - 🔄 Starting iteration 3/3
2025-06-29 02:14:59 - LoopExecutor - DEBUG - 🔄 Executing iteration 3 chain directly
2025-06-29 02:14:59 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in memory: healthcare
2025-06-29 02:14:59 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_2
2025-06-29 02:14:59 - RedisManager - DEBUG - Set key 'result:loop_iteration_2' with TTL of 900 seconds
2025-06-29 02:14:59 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:14:59 - StateManager - INFO - Marked transition loop_iteration_2 as completed (was_pending=False, was_waiting=False)
2025-06-29 02:14:59 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'loop_iteration_1', 'transition-MCP_script-generation-mcp_script_generate-*************', 'transition-CombineTextComponent-*************', 'loop_iteration_2', 'current_iteration', 'transition-CombineTextComponent-*************'}
2025-06-29 02:14:59 - StateManager - DEBUG - Stored result for transition current_iteration in memory: healthcare
2025-06-29 02:15:00 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-29 02:15:00 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-29 02:15:00 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:15:00 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-29 02:15:00 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'loop_iteration_1', 'transition-MCP_script-generation-mcp_script_generate-*************', 'transition-CombineTextComponent-*************', 'loop_iteration_2', 'current_iteration', 'transition-CombineTextComponent-*************'}
2025-06-29 02:15:00 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-29 02:15:00 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 'healthcare', 'iteration_index': 2, 'iteration_metadata': {'timestamp': 278186.864284041, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 02:15:00 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_2
2025-06-29 02:15:00 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-CombineTextComponent-*************
2025-06-29 02:15:00 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-CombineTextComponent-*************
2025-06-29 02:15:00 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-29 02:15:00 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 26, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:15:00 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 26, 'workflow_status': 'running'}
2025-06-29 02:15:00 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-29 02:15:00 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-29 02:15:00 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-29 02:15:00 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-29 02:15:00 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-29 02:15:01 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-06-29 02:15:03 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b
2025-06-29 02:15:04 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-06-29 02:15:04 - StateManager - INFO - Using in-memory result for transition transition-LoopNode-*************.
2025-06-29 02:15:04 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-29 02:15:04 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'healthcare', 'iteration_index': 2, 'iteration_metadata': {'timestamp': 278186.864284041, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-29 02:15:04 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-29 02:15:04 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'healthcare', 'iteration_index': 2, 'iteration_metadata': {'timestamp': 278186.864284041, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': healthcare
2025-06-29 02:15:04 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-29 02:15:04 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-29 02:15:04 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-29 02:15:04 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-29 02:15:04 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-29 02:15:04 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-29 02:15:04 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'healthcare', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 02:15:04 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'healthcare', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 02:15:04 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'healthcare', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-29 02:15:04 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 27, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:15:04 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 27, 'workflow_status': 'running'}
2025-06-29 02:15:04 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: d9b5597a-3136-450c-a341-00ef059bd6ad) using provided producer.
2025-06-29 02:15:04 - NodeExecutor - DEBUG - Added correlation_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b to payload
2025-06-29 02:15:04 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-29 02:15:04 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'healthcare', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': 'd9b5597a-3136-450c-a341-00ef059bd6ad', 'correlation_id': '154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-29 02:15:04 - NodeExecutor - DEBUG - Request d9b5597a-3136-450c-a341-00ef059bd6ad sent successfully using provided producer.
2025-06-29 02:15:04 - NodeExecutor - DEBUG - Waiting indefinitely for result for request d9b5597a-3136-450c-a341-00ef059bd6ad...
2025-06-29 02:15:04 - NodeExecutor - DEBUG - Result consumer received message: Offset=931
2025-06-29 02:15:04 - NodeExecutor - DEBUG - Received valid result for request_id d9b5597a-3136-450c-a341-00ef059bd6ad
2025-06-29 02:15:04 - NodeExecutor - INFO - Result received for request d9b5597a-3136-450c-a341-00ef059bd6ad.
2025-06-29 02:15:04 - TransitionHandler - INFO - Execution result from Components executor: "healthcare and AI"
2025-06-29 02:15:04 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 28, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:15:04 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'healthcare and AI', 'status': 'completed', 'sequence': 28, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 02:15:04 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'healthcare and AI'}, 'status': 'completed', 'timestamp': **********.5967438}}
2025-06-29 02:15:05 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-29 02:15:05 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-29 02:15:05 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:15:05 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 02:15:05 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'loop_iteration_1', 'transition-MCP_script-generation-mcp_script_generate-*************', 'transition-CombineTextComponent-*************', 'loop_iteration_2', 'current_iteration', 'transition-CombineTextComponent-*************'}
2025-06-29 02:15:05 - LoopExecutor - INFO - 🚨 notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-29 02:15:05 - TransitionHandler - ERROR - ❌ Error notifying loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************: object NoneType can't be used in 'await' expression
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:15:05 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 4.68 seconds
2025-06-29 02:15:05 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 29, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:15:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Completed transition in 4.68 seconds', 'message': 'Transition completed in 4.68 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 29, 'workflow_status': 'running'}
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-29 02:15:05 - LoopExecutor - DEBUG - 🔧 Transition transition-CombineTextComponent-************* execution completed, result type: <class 'list'>
2025-06-29 02:15:05 - LoopExecutor - DEBUG - 🔧 Processing result for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:15:05 - LoopExecutor - DEBUG - 🔗 Transition transition-CombineTextComponent-************* returned next transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:15:05 - LoopExecutor - DEBUG - 🔧 Extended next_transitions, now: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:15:05 - LoopExecutor - DEBUG - 🔗 Moving to next transitions in chain: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:15:05 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:15:05 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:15:05 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:15:05 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 30, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:15:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Starting execution of transition: transition-MCP_script-generation-mcp_script_generate-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************', 'status': 'started', 'sequence': 30, 'workflow_status': 'running'}
2025-06-29 02:15:05 - TransitionHandler - EXECUTE - Transition 'transition-MCP_script-generation-mcp_script_generate-*************' (type=standard, execution_type=MCP)
2025-06-29 02:15:05 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: MCP
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: mcp
2025-06-29 02:15:05 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for mcp node
2025-06-29 02:15:06 - StateManager - DEBUG - Retrieved result for transition transition-CombineTextComponent-************* from Redis
2025-06-29 02:15:06 - StateManager - DEBUG - Detected wrapped result structure for transition transition-CombineTextComponent-*************, extracting data
2025-06-29 02:15:06 - StateManager - DEBUG - Extracted double-nested result data for transition transition-CombineTextComponent-*************
2025-06-29 02:15:06 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-CombineTextComponent-*************
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-CombineTextComponent-************* (total: 1)
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'result': {'result': 'healthcare and AI'}
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle result: healthcare and AI
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - Found result.result: healthcare and AI (type: <class 'str'>)
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - No handle matches found for 'result', treating result as single-value
2025-06-29 02:15:06 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-29 02:15:06 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'result': {'result': 'healthcare and AI'}
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle result: healthcare and AI
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - Found result.result: healthcare and AI (type: <class 'str'>)
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - No handle matches found for 'result', treating result as single-value
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - ✅ Handle mapping success: result → topic via path 'result': healthcare and AI
2025-06-29 02:15:06 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-29 02:15:06 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - Filtering out field 'topic' with null/empty value: None
2025-06-29 02:15:06 - WorkflowUtils - DEBUG - Filtering out field 'link' with null/empty value: None
2025-06-29 02:15:06 - WorkflowUtils - INFO - 🧹 Parameter filtering: 5 → 3 fields (2 null/empty fields removed)
2025-06-29 02:15:06 - TransitionHandler - DEBUG - 📌 Added static parameter: script_type = TOPIC
2025-06-29 02:15:06 - TransitionHandler - DEBUG - 📌 Added static parameter: keywords = 1 min marketing story
2025-06-29 02:15:06 - TransitionHandler - DEBUG - 📌 Added static parameter: video_type = SHORT
2025-06-29 02:15:06 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'topic': 'healthcare and AI', 'script_type': 'TOPIC', 'keywords': '1 min marketing story', 'video_type': 'SHORT'}
2025-06-29 02:15:06 - TransitionHandler - DEBUG - tool Parameters: {'topic': 'healthcare and AI', 'script_type': 'TOPIC', 'keywords': '1 min marketing story', 'video_type': 'SHORT'}
2025-06-29 02:15:06 - TransitionHandler - INFO - Invoking tool 'script_generate' (tool_id: 1) for node '748a8221-d7d9-4352-93ae-00700f4d28b1' in transition 'transition-MCP_script-generation-mcp_script_generate-*************' with parameters: {'topic': 'healthcare and AI', 'script_type': 'TOPIC', 'keywords': '1 min marketing story', 'video_type': 'SHORT'}
2025-06-29 02:15:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 31, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:15:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************', 'node_id': '748a8221-d7d9-4352-93ae-00700f4d28b1', 'tool_name': 'script_generate', 'message': 'Connecting to server', 'result': 'Connecting to server 748a8221-d7d9-4352-93ae-00700f4d28b1', 'status': 'connecting', 'sequence': 31, 'workflow_status': 'running'}
2025-06-29 02:15:06 - MCPToolExecutor - INFO - Executing tool 'script_generate' via Kafka (request_id: ed98dfb2-17c1-4426-87ab-739167a9d678) with correlation_id: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, user_id: c1454e90-09ac-40f2-bde2-833387d7b645, mcp_id: 748a8221-d7d9-4352-93ae-00700f4d28b1 using provided producer.
2025-06-29 02:15:06 - MCPToolExecutor - DEBUG - Added correlation_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b to payload
2025-06-29 02:15:06 - MCPToolExecutor - DEBUG - Added user_id c1454e90-09ac-40f2-bde2-833387d7b645 to payload
2025-06-29 02:15:06 - MCPToolExecutor - DEBUG - Added mcp_id 748a8221-d7d9-4352-93ae-00700f4d28b1 to payload
2025-06-29 02:15:06 - MCPToolExecutor - DEBUG - Added transition_id transition-MCP_script-generation-mcp_script_generate-************* to payload
2025-06-29 02:15:06 - MCPToolExecutor - DEBUG - Sending request to topic 'mcp-execution-request': {'tool_name': 'script_generate', 'tool_parameters': {'topic': 'healthcare and AI', 'script_type': 'TOPIC', 'keywords': '1 min marketing story', 'video_type': 'SHORT'}, 'request_id': 'ed98dfb2-17c1-4426-87ab-739167a9d678', 'correlation_id': '154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b', 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645', 'mcp_id': '748a8221-d7d9-4352-93ae-00700f4d28b1', 'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************'}
2025-06-29 02:15:06 - MCPToolExecutor - DEBUG - Request ed98dfb2-17c1-4426-87ab-739167a9d678 sent successfully using provided producer.
2025-06-29 02:15:06 - MCPToolExecutor - DEBUG - Waiting indefinitely for result for request ed98dfb2-17c1-4426-87ab-739167a9d678...
2025-06-29 02:15:22 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 02:15:23 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 02:15:23 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 02:15:23 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 02:16:22 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 02:16:23 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 02:16:23 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 02:16:23 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 02:16:48 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:baeda91c-a099-4bf0-952c-f70abee50d09', 'data': b'expired'}
2025-06-29 02:16:48 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:baeda91c-a099-4bf0-952c-f70abee50d09'
2025-06-29 02:16:48 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:baeda91c-a099-4bf0-952c-f70abee50d09
2025-06-29 02:16:48 - RedisEventListener - DEBUG - Extracted key: workflow_state:baeda91c-a099-4bf0-952c-f70abee50d09
2025-06-29 02:16:48 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-29 02:16:48 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-29 02:16:48 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: baeda91c-a099-4bf0-952c-f70abee50d09
2025-06-29 02:16:48 - RedisEventListener - INFO - Archiving workflow state for workflow: baeda91c-a099-4bf0-952c-f70abee50d09
2025-06-29 02:16:51 - MCPToolExecutor - DEBUG - Result consumer received message: Offset=1016
2025-06-29 02:16:51 - MCPToolExecutor - DEBUG - Received valid result for request_id ed98dfb2-17c1-4426-87ab-739167a9d678
2025-06-29 02:16:51 - MCPToolExecutor - INFO - Result received for request ed98dfb2-17c1-4426-87ab-739167a9d678.
2025-06-29 02:16:51 - TransitionHandler - INFO - Execution result from MCP executor: [
  {
    "title": "healthcare and AI",
    "script": "AI is changing healthcare by making diagnoses quicker and treatments more personalized. Picture quicker, spot-on diagnostics with AI tools.... Isn't that exciting? AI's about to put health management front and center.\n\nStay curious? Watch and learn more!\n\nWow, just wow. Like this for more insights!"
  }
]
2025-06-29 02:16:51 - HelperFunctions - DEBUG - Property: title Value: healthcare and AI
2025-06-29 02:16:51 - HelperFunctions - DEBUG - Property 'title' found in schema description
2025-06-29 02:16:51 - HelperFunctions - DEBUG - Data type set to 'string' based on schema
2025-06-29 02:16:51 - HelperFunctions - DEBUG - Property: script Value: AI is changing healthcare by making diagnoses quicker and treatments more personalized. Picture quicker, spot-on diagnostics with AI tools.... Isn't that exciting? AI's about to put health management front and center.

Stay curious? Watch and learn more!

Wow, just wow. Like this for more insights!
2025-06-29 02:16:51 - HelperFunctions - DEBUG - Property 'script' found in schema description
2025-06-29 02:16:51 - HelperFunctions - DEBUG - Data type set to 'string' based on schema
2025-06-29 02:16:51 - TransitionHandler - DEBUG - Formatted result with semantic types: [{'data': 'healthcare and AI', 'data_type': 'string', 'property_name': 'title', 'semantic_type': 'string'}, {'data': "AI is changing healthcare by making diagnoses quicker and treatments more personalized. Picture quicker, spot-on diagnostics with AI tools.... Isn't that exciting? AI's about to put health management front and center.\n\nStay curious? Watch and learn more!\n\nWow, just wow. Like this for more insights!", 'data_type': 'string', 'property_name': 'script', 'semantic_type': 'string'}]
2025-06-29 02:16:51 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 32, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:16:51 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************', 'node_id': '748a8221-d7d9-4352-93ae-00700f4d28b1', 'tool_name': 'script_generate', 'message': 'Transition Result received.', 'result': [{'data': 'healthcare and AI', 'data_type': 'string', 'property_name': 'title', 'semantic_type': 'string'}, {'data': "AI is changing healthcare by making diagnoses quicker and treatments more personalized. Picture quicker, spot-on diagnostics with AI tools.... Isn't that exciting? AI's about to put health management front and center.\n\nStay curious? Watch and learn more!\n\nWow, just wow. Like this for more insights!", 'data_type': 'string', 'property_name': 'script', 'semantic_type': 'string'}], 'status': 'completed', 'sequence': 32, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 02:16:51 - StateManager - DEBUG - Stored result for transition transition-MCP_script-generation-mcp_script_generate-************* in memory: {'script_generate': {'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************', 'node_id': '748a8221-d7d9-4352-93ae-00700f4d28b1', 'tool_name': 'script_generate', 'result': {'result': [{'title': 'healthcare and AI', 'script': "AI is changing healthcare by making diagnoses quicker and treatments more personalized. Picture quicker, spot-on diagnostics with AI tools.... Isn't that exciting? AI's about to put health management front and center.\n\nStay curious? Watch and learn more!\n\nWow, just wow. Like this for more insights!"}]}, 'status': 'completed', 'timestamp': **********.3415911}}
2025-06-29 02:16:52 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MCP_script-generation-mcp_script_generate-*************'
2025-06-29 02:16:53 - RedisManager - DEBUG - Set key 'result:transition-MCP_script-generation-mcp_script_generate-*************' with TTL of 300 seconds
2025-06-29 02:16:53 - StateManager - DEBUG - Stored result for transition transition-MCP_script-generation-mcp_script_generate-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:16:53 - StateManager - INFO - Marked transition transition-MCP_script-generation-mcp_script_generate-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 02:16:53 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'loop_iteration_1', 'transition-MCP_script-generation-mcp_script_generate-*************', 'transition-CombineTextComponent-*************', 'loop_iteration_2', 'current_iteration', 'transition-CombineTextComponent-*************'}
2025-06-29 02:16:53 - LoopExecutor - INFO - 🚨 notify_transition_completion called for transition transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:16:53 - TransitionHandler - ERROR - ❌ Error notifying loop executor transition-LoopNode-************* about transition transition-MCP_script-generation-mcp_script_generate-*************: object NoneType can't be used in 'await' expression
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'list'>
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'list'>
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MCP_script-generation-mcp_script_generate-*************:
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MCP_script-generation-mcp_script_generate-*************, returning empty list
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-CombineTextComponent-*************']
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-CombineTextComponent-*************']
2025-06-29 02:16:53 - TransitionHandler - INFO - Completed transition transition-MCP_script-generation-mcp_script_generate-************* in 107.72 seconds
2025-06-29 02:16:53 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 33, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:16:53 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Completed transition in 107.72 seconds', 'message': 'Transition completed in 107.72 seconds', 'transition_id': 'transition-MCP_script-generation-mcp_script_generate-*************', 'status': 'time_logged', 'sequence': 33, 'workflow_status': 'running'}
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-MCP_script-generation-mcp_script_generate-*************: ['transition-CombineTextComponent-*************']
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-29 02:16:53 - LoopExecutor - DEBUG - 🔧 Transition transition-MCP_script-generation-mcp_script_generate-************* execution completed, result type: <class 'list'>
2025-06-29 02:16:53 - LoopExecutor - DEBUG - 🔧 Processing result for transition-MCP_script-generation-mcp_script_generate-*************: ['transition-CombineTextComponent-*************']
2025-06-29 02:16:53 - LoopExecutor - DEBUG - 🔗 Transition transition-MCP_script-generation-mcp_script_generate-************* returned next transitions: ['transition-CombineTextComponent-*************']
2025-06-29 02:16:53 - LoopExecutor - DEBUG - 🔧 Extended next_transitions, now: ['transition-CombineTextComponent-*************']
2025-06-29 02:16:53 - LoopExecutor - DEBUG - 🔗 Moving to next transitions in chain: ['transition-CombineTextComponent-*************']
2025-06-29 02:16:53 - LoopExecutor - DEBUG - 🏁 Found exit transitions to execute: ['transition-CombineTextComponent-*************']
2025-06-29 02:16:53 - LoopExecutor - DEBUG - 🚀 Executing exit transition: transition-CombineTextComponent-*************
2025-06-29 02:16:53 - LoopExecutor - DEBUG - 🔧 About to execute exit transition transition-CombineTextComponent-*************
2025-06-29 02:16:53 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-29 02:16:53 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 34, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:16:53 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 34, 'workflow_status': 'running'}
2025-06-29 02:16:53 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-29 02:16:53 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-29 02:16:53 - StateManager - DEBUG - Retrieved result for transition transition-MCP_script-generation-mcp_script_generate-************* from Redis
2025-06-29 02:16:53 - StateManager - DEBUG - Detected wrapped result structure for transition transition-MCP_script-generation-mcp_script_generate-*************, extracting data
2025-06-29 02:16:53 - StateManager - DEBUG - Extracted double-nested result data for transition transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:16:53 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-MCP_script-generation-mcp_script_generate-*************
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-MCP_script-generation-mcp_script_generate-************* (total: 1)
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'title': {'result': [{'title': 'healthcare and AI', 'script': "AI is changing healthcare by making diagnoses quicker and treatments more personalized. Picture quicker, spot-on diagnostics with AI tools.... Isn't that exciting? AI's about to put health management front and center.\n\nStay curious? Watch and learn more!\n\nWow, just wow. Like this for more insights!"}]}
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle title: {'title': 'healthcare and AI', 'script': "AI is changing healthcare by making diagnoses quicker and treatments more personalized. Picture quicker, spot-on diagnostics with AI tools.... Isn't that exciting? AI's about to put health management front and center.\n\nStay curious? Watch and learn more!\n\nWow, just wow. Like this for more insights!"}
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Found result.result: {'title': 'healthcare and AI', 'script': "AI is changing healthcare by making diagnoses quicker and treatments more personalized. Picture quicker, spot-on diagnostics with AI tools.... Isn't that exciting? AI's about to put health management front and center.\n\nStay curious? Watch and learn more!\n\nWow, just wow. Like this for more insights!"} (type: <class 'dict'>)
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Found handle 'title' directly in dict
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Successfully extracted handle 'title' with path 'result.title': healthcare and AI
2025-06-29 02:16:53 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-29 02:16:53 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'title': {'result': [{'title': 'healthcare and AI', 'script': "AI is changing healthcare by making diagnoses quicker and treatments more personalized. Picture quicker, spot-on diagnostics with AI tools.... Isn't that exciting? AI's about to put health management front and center.\n\nStay curious? Watch and learn more!\n\nWow, just wow. Like this for more insights!"}]}
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle title: {'title': 'healthcare and AI', 'script': "AI is changing healthcare by making diagnoses quicker and treatments more personalized. Picture quicker, spot-on diagnostics with AI tools.... Isn't that exciting? AI's about to put health management front and center.\n\nStay curious? Watch and learn more!\n\nWow, just wow. Like this for more insights!"}
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Found result.result: {'title': 'healthcare and AI', 'script': "AI is changing healthcare by making diagnoses quicker and treatments more personalized. Picture quicker, spot-on diagnostics with AI tools.... Isn't that exciting? AI's about to put health management front and center.\n\nStay curious? Watch and learn more!\n\nWow, just wow. Like this for more insights!"} (type: <class 'dict'>)
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Found handle 'title' directly in dict
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Successfully extracted handle 'title' with path 'result.title': healthcare and AI
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - ✅ Handle mapping success: title → main_input via path 'result.title': healthcare and AI
2025-06-29 02:16:53 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-29 02:16:53 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Filtering out field 'num_additional_inputs' with null/empty value: None
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: None
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: None
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: None
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: None
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: None
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: None
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: None
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: None
2025-06-29 02:16:53 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: None
2025-06-29 02:16:53 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 0 fields (13 null/empty fields removed)
2025-06-29 02:16:53 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'healthcare and AI'}
2025-06-29 02:16:53 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'healthcare and AI'}
2025-06-29 02:16:53 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'healthcare and AI'}
2025-06-29 02:16:53 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 35, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:16:53 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 35, 'workflow_status': 'running'}
2025-06-29 02:16:53 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 9f212f24-9ba6-425f-ab83-83dad8c685fa) using provided producer.
2025-06-29 02:16:53 - NodeExecutor - DEBUG - Added correlation_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b to payload
2025-06-29 02:16:53 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-29 02:16:53 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'healthcare and AI'}, 'request_id': '9f212f24-9ba6-425f-ab83-83dad8c685fa', 'correlation_id': '154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-29 02:16:53 - NodeExecutor - DEBUG - Request 9f212f24-9ba6-425f-ab83-83dad8c685fa sent successfully using provided producer.
2025-06-29 02:16:53 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 9f212f24-9ba6-425f-ab83-83dad8c685fa...
2025-06-29 02:16:54 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-29 02:16:54 - NodeExecutor - DEBUG - Result consumer received message: Offset=932
2025-06-29 02:16:54 - NodeExecutor - DEBUG - Received valid result for request_id 9f212f24-9ba6-425f-ab83-83dad8c685fa
2025-06-29 02:16:54 - NodeExecutor - INFO - Result received for request 9f212f24-9ba6-425f-ab83-83dad8c685fa.
2025-06-29 02:16:54 - TransitionHandler - INFO - Execution result from Components executor: "healthcare and AI"
2025-06-29 02:16:54 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 36, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:16:54 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'healthcare and AI', 'status': 'completed', 'sequence': 36, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 02:16:54 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'healthcare and AI'}, 'status': 'completed', 'timestamp': **********.4540842}}
2025-06-29 02:16:54 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-29 02:16:55 - PostgresManager - DEBUG - Inserted new workflow state for correlation_id: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b
2025-06-29 02:16:55 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-29 02:16:55 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:16:55 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 02:16:55 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'loop_iteration_1', 'transition-MCP_script-generation-mcp_script_generate-*************', 'transition-CombineTextComponent-*************', 'loop_iteration_2', 'current_iteration', 'transition-CombineTextComponent-*************'}
2025-06-29 02:16:55 - LoopExecutor - INFO - 🚨 notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-29 02:16:55 - TransitionHandler - ERROR - ❌ Error notifying loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************: object NoneType can't be used in 'await' expression
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 0
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: []
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔗 Final next_transitions: []
2025-06-29 02:16:55 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.12 seconds
2025-06-29 02:16:55 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 37, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:16:55 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Completed transition in 2.12 seconds', 'message': 'Transition completed in 2.12 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 37, 'workflow_status': 'running'}
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: []
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 0
2025-06-29 02:16:55 - LoopExecutor - DEBUG - 🔧 Exit transition transition-CombineTextComponent-************* execution completed, result type: <class 'list'>
2025-06-29 02:16:55 - LoopExecutor - DEBUG - ✅ Exit transition transition-CombineTextComponent-************* completed with result: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'healthcare and AI'}, 'status': 'completed', 'timestamp': **********.4540842}}
2025-06-29 02:16:55 - LoopExecutor - DEBUG - 🎯 Extracted final result from exit transition: healthcare and AI
2025-06-29 02:16:55 - LoopExecutor - DEBUG - 🔚 All exit transitions executed, chain complete
2025-06-29 02:16:55 - LoopExecutor - DEBUG - 🔚 Chain execution completed, final_result: healthcare and AI
2025-06-29 02:16:55 - LoopExecutor - INFO - ✅ Iteration 3 completed successfully
2025-06-29 02:16:55 - LoopExecutor - INFO - 🔄 CALLING NEW AGGREGATION METHOD with 3 iteration results
2025-06-29 02:16:55 - LoopExecutor - DEBUG - 🔄 Aggregating 3 iteration results using aggregation_type: collect_all
2025-06-29 02:16:55 - LoopExecutor - DEBUG - 🔍 Extracted 0 data items from iterations
2025-06-29 02:16:55 - LoopExecutor - DEBUG - 🔍 Sample extracted data: None
2025-06-29 02:16:55 - LoopExecutor - INFO - 🔄 NEW AGGREGATION METHOD RETURNED: []
2025-06-29 02:16:55 - LoopExecutor - INFO - 📊 Loop execution completed with metadata: {'success': True, 'final_results': [], 'iteration_count': 3, 'total_iterations': 3}
2025-06-29 02:16:55 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 38, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:16:55 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'status': 'loop_completed', 'metadata': {'success': True, 'final_results': [], 'iteration_count': 3, 'total_iterations': 3}, 'transition_id': 'transition-LoopNode-*************', 'sequence': 38, 'workflow_status': 'running'}
2025-06-29 02:16:55 - LoopExecutor - INFO - ✅ Loop node execution completed for transition: transition-LoopNode-*************
2025-06-29 02:16:55 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-29 02:16:55 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-29 02:16:55 - TransitionHandler - INFO - Execution result from loop executor: {
  "final_results": []
}
2025-06-29 02:16:55 - TransitionHandler - INFO - Checking execution result for errors: {
  "final_results": []
}
2025-06-29 02:16:55 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 39, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:16:55 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition Result received.', 'result': {'final_results': []}, 'status': 'completed', 'sequence': 39, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 02:16:55 - TransitionHandler - INFO - 🔄 Handling loop completion for transition: transition-LoopNode-*************
2025-06-29 02:16:55 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'final_results': []}
2025-06-29 02:16:55 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b
2025-06-29 02:16:56 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-29 02:16:56 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-29 02:16:56 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:16:56 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 02:16:56 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'loop_iteration_1', 'transition-MCP_script-generation-mcp_script_generate-*************', 'transition-CombineTextComponent-*************', 'loop_iteration_2', 'current_iteration', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************'}
2025-06-29 02:16:56 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-29 02:16:56 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-29 02:16:56 - TransitionHandler - INFO - 🔍 Auto-detected loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-29 02:16:56 - TransitionHandler - DEBUG - 🔍 Loop completion filtering - Loop config: {'iteration_behavior': 'independent', 'iteration_source': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-06-29 02:16:56 - TransitionHandler - DEBUG - 🔍 Loop body transitions to filter: ['transition-CombineTextComponent-*************']
2025-06-29 02:16:56 - TransitionHandler - DEBUG - 🔍 Output data configs: [{'to_transition_id': 'transition-CombineTextComponent-*************', 'target_node_id': 'Combine Text', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'current_item', 'result_path': 'current_item', 'edge_id': 'reactflow__edge-LoopNode-*************current_item-CombineTextComponent-*************main_input'}]}}, {'to_transition_id': 'transition-MergeDataComponent-*************', 'target_node_id': 'Merge Data', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'final_results', 'result_path': 'final_results', 'edge_id': 'reactflow__edge-LoopNode-*************final_results-MergeDataComponent-*************main_input'}]}}]
2025-06-29 02:16:56 - TransitionHandler - DEBUG - 🚫 Skipping loop body transition 'transition-CombineTextComponent-*************' - should have been executed internally by loop executor
2025-06-29 02:16:56 - TransitionHandler - DEBUG - ✅ Adding exit transition 'transition-MergeDataComponent-*************' to next transitions
2025-06-29 02:16:56 - TransitionHandler - INFO - ✅ Loop completion handled for transition-LoopNode-*************. Next transitions: ['transition-MergeDataComponent-*************']
2025-06-29 02:16:56 - TransitionHandler - INFO - Completed transition transition-LoopNode-************* in 235.63 seconds
2025-06-29 02:16:56 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 40, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:16:56 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Completed transition in 235.63 seconds', 'message': 'Transition completed in 235.63 seconds', 'transition_id': 'transition-LoopNode-*************', 'status': 'time_logged', 'sequence': 40, 'workflow_status': 'running'}
2025-06-29 02:16:56 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-LoopNode-*************: ['transition-MergeDataComponent-*************']
2025-06-29 02:16:56 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-29 02:16:56 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-MergeDataComponent-*************']]
2025-06-29 02:16:56 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-LoopNode-*************: ['transition-MergeDataComponent-*************']
2025-06-29 02:16:56 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-06-29 02:16:56 - EnhancedWorkflowEngine - INFO - Transition transition-LoopNode-************* completed successfully: 1 next transitions
2025-06-29 02:16:56 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-MergeDataComponent-*************']
2025-06-29 02:16:56 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-MergeDataComponent-*************']
2025-06-29 02:16:56 - EnhancedWorkflowEngine - INFO - Adding transition transition-MergeDataComponent-************* to pending (all dependencies met)
2025-06-29 02:16:56 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-06-29 02:16:57 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b'
2025-06-29 02:16:57 - RedisManager - DEBUG - Set key 'workflow_state:154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b' with TTL of 600 seconds
2025-06-29 02:16:57 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:16:57 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-29 02:16:57 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-29 02:16:57 - StateManager - INFO - Cleared 1 pending transitions: {'transition-MergeDataComponent-*************'}
2025-06-29 02:16:57 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-29 02:16:57 - StateManager - INFO - Terminated: False
2025-06-29 02:16:57 - StateManager - INFO - Pending transitions (0): []
2025-06-29 02:16:57 - StateManager - INFO - Waiting transitions (0): []
2025-06-29 02:16:57 - StateManager - INFO - Completed transitions (8): ['current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'transition-CombineTextComponent-*************', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************', 'transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-29 02:16:57 - StateManager - INFO - Results stored for 8 transitions
2025-06-29 02:16:57 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-29 02:16:57 - StateManager - INFO - Workflow status: inactive
2025-06-29 02:16:57 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-29 02:16:57 - StateManager - INFO - Workflow status: inactive
2025-06-29 02:16:57 - StateManager - INFO - Workflow paused: False
2025-06-29 02:16:57 - StateManager - INFO - ==============================
2025-06-29 02:16:57 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-06-29 02:16:57 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 41, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:16:57 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 41, 'workflow_status': 'running'}
2025-06-29 02:16:57 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=standard, execution_type=Components)
2025-06-29 02:16:57 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-29 02:16:57 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-06-29 02:16:57 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-29 02:16:57 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-29 02:16:58 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-29 02:16:58 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-29 02:16:58 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': []}
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-29 02:16:58 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-29 02:16:58 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': []}
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_results → main_input via path 'final_results': []
2025-06-29 02:16:58 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-29 02:16:58 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: []
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-06-29 02:16:58 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-06-29 02:16:58 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 2 fields (22 null/empty fields removed)
2025-06-29 02:16:58 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-29 02:16:58 - TransitionHandler - DEBUG - 📌 Added static parameter: merge_strategy = Aggregate
2025-06-29 02:16:58 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-29 02:16:58 - TransitionHandler - DEBUG - tool Parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-29 02:16:58 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-29 02:16:58 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 42, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:16:58 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server MergeDataComponent', 'status': 'connecting', 'sequence': 42, 'workflow_status': 'running'}
2025-06-29 02:16:58 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: 190e88c0-f06f-41ee-8ffe-76e7358709e2) using provided producer.
2025-06-29 02:16:58 - NodeExecutor - DEBUG - Added correlation_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b to payload
2025-06-29 02:16:58 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-06-29 02:16:58 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}, 'request_id': '190e88c0-f06f-41ee-8ffe-76e7358709e2', 'correlation_id': '154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b', 'transition_id': 'transition-MergeDataComponent-*************'}
2025-06-29 02:16:58 - NodeExecutor - DEBUG - Request 190e88c0-f06f-41ee-8ffe-76e7358709e2 sent successfully using provided producer.
2025-06-29 02:16:58 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 190e88c0-f06f-41ee-8ffe-76e7358709e2...
2025-06-29 02:16:58 - NodeExecutor - DEBUG - Result consumer received message: Offset=933
2025-06-29 02:16:58 - NodeExecutor - DEBUG - Received valid result for request_id 190e88c0-f06f-41ee-8ffe-76e7358709e2
2025-06-29 02:16:59 - NodeExecutor - INFO - Result received for request 190e88c0-f06f-41ee-8ffe-76e7358709e2.
2025-06-29 02:16:59 - TransitionHandler - INFO - Execution result from Components executor: []
2025-06-29 02:16:59 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 43, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:16:59 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': [], 'status': 'completed', 'sequence': 43, 'workflow_status': 'running', 'approval_required': False}
2025-06-29 02:16:59 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': []}, 'status': 'completed', 'timestamp': 1751143619.0004349}}
2025-06-29 02:16:59 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-06-29 02:16:59 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-06-29 02:16:59 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-29 02:16:59 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-29 02:16:59 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'transition-MergeDataComponent-*************', 'loop_iteration_1', 'transition-MCP_script-generation-mcp_script_generate-*************', 'transition-CombineTextComponent-*************', 'loop_iteration_2', 'current_iteration', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************'}
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'list'>
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'list'>
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-MergeDataComponent-*************
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 0
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: []
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔗 Final next_transitions: []
2025-06-29 02:16:59 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 2.17 seconds
2025-06-29 02:16:59 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 44, corr_id 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b):
2025-06-29 02:16:59 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'result': 'Completed transition in 2.17 seconds', 'message': 'Transition completed in 2.17 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 44, 'workflow_status': 'running'}
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-MergeDataComponent-*************: []
2025-06-29 02:16:59 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 0
2025-06-29 02:16:59 - EnhancedWorkflowEngine - DEBUG - Results: [[]]
2025-06-29 02:16:59 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-MergeDataComponent-*************: []
2025-06-29 02:16:59 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-06-29 02:16:59 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-29 02:16:59 - EnhancedWorkflowEngine - INFO - Workflow is marked as terminated, ending workflow execution.
2025-06-29 02:16:59 - StateManager - DEBUG - Workflow active: False (terminated=True, pending=empty, waiting=empty)
2025-06-29 02:16:59 - EnhancedWorkflowEngine - INFO - Workflow execution completed.
2025-06-29 02:16:59 - KafkaWorkflowConsumer - INFO - Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' final status: completed, result: Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' executed successfully.
2025-06-29 02:16:59 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b, response: {'status': 'complete', 'result': "Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' executed successfully.", 'workflow_status': 'completed'}
2025-06-29 02:16:59 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b 
2025-06-29 02:17:22 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 02:17:23 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 02:17:23 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 02:17:23 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 02:18:22 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 02:18:23 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 02:18:23 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 02:18:23 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 02:19:22 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-29 02:19:23 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 02:19:23 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-29 02:19:23 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-29 02:20:05 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-CombineTextComponent-*************', 'data': b'expired'}
2025-06-29 02:20:05 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-CombineTextComponent-*************'
2025-06-29 02:20:05 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-CombineTextComponent-*************
2025-06-29 02:20:05 - RedisEventListener - DEBUG - Extracted key: result:transition-CombineTextComponent-*************
2025-06-29 02:20:05 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-29 02:20:05 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-29 02:20:05 - RedisEventListener - INFO - Detected expired event for result of transition: transition-CombineTextComponent-*************
2025-06-29 02:20:05 - RedisEventListener - INFO - Archiving result for transition: transition-CombineTextComponent-*************
2025-06-29 02:20:05 - StateManager - DEBUG - Attempting to archive result for transition transition-CombineTextComponent-*************
2025-06-29 02:20:06 - StateManager - DEBUG - Provided result: False
2025-06-29 02:20:06 - StateManager - DEBUG - Trying to get result from Redis for transition transition-CombineTextComponent-*************
2025-06-29 02:20:07 - StateManager - DEBUG - No result found in Redis for transition transition-CombineTextComponent-*************
2025-06-29 02:20:07 - StateManager - DEBUG - Trying to get result from memory for transition transition-CombineTextComponent-*************
2025-06-29 02:20:07 - StateManager - DEBUG - Found result in memory for transition transition-CombineTextComponent-*************
2025-06-29 02:20:07 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-CombineTextComponent-*************
2025-06-29 02:20:07 - PostgresManager - DEBUG - Attempting to store transition result for transition-CombineTextComponent-************* in correlation 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b
2025-06-29 02:20:07 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-29 02:20:07 - PostgresManager - DEBUG - Result data: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'healthcare and AI'}, 'status': 'completed', 'timestamp': **********.5967438}}
2025-06-29 02:20:10 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-29 02:20:10 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-29 02:20:10 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-29 02:20:10 - PostgresManager - DEBUG - Inserting new record for transition transition-CombineTextComponent-*************
2025-06-29 02:20:11 - PostgresManager - DEBUG - Inserted new result for transition transition-CombineTextComponent-************* in correlation 154441ed-00e5-4cfc-8cbf-0fc6ea70fd5b
2025-06-29 02:20:11 - PostgresManager - DEBUG - Successfully stored transition result for transition-CombineTextComponent-*************
2025-06-29 02:20:11 - StateManager - INFO - Archived result for transition transition-CombineTextComponent-************* to PostgreSQL
2025-06-29 02:20:20 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-29 02:20:21 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-29 02:20:21 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-29 02:20:21 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-29 02:20:21 - Main - ERROR - Shutting down due to keyboard interrupt...
