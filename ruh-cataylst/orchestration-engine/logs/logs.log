2025-06-28 23:41:22 - Main - INFO - Starting Server
2025-06-28 23:41:22 - Main - INFO - Connection at: **************:9092
2025-06-28 23:41:22 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 23:41:22 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-28 23:41:22 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 23:41:22 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 23:41:22 - <PERSON><PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 23:41:24 - <PERSON><PERSON>Manager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 23:41:24 - Red<PERSON>Manager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 23:41:25 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - Successfully connected to Redis on DB index: 6!
