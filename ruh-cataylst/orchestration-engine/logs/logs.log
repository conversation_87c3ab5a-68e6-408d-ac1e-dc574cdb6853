2025-06-28 23:01:05 - Main - INFO - Starting Server
2025-06-28 23:01:05 - Main - INFO - Connection at: **************:9092
2025-06-28 23:01:05 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 23:01:05 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-28 23:01:05 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 23:01:05 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 23:01:05 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 23:01:07 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 23:01:07 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 23:01:09 - Red<PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 23:01:11 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-28 23:01:11 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-28 23:01:13 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 23:01:14 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-28 23:01:14 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 23:01:16 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 23:01:16 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 23:01:17 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 23:01:17 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-28 23:01:17 - RedisEventListener - INFO - Redis event listener started
2025-06-28 23:01:17 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-28 23:01:17 - StateManager - DEBUG - Using provided database connections
2025-06-28 23:01:17 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 23:01:17 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 23:01:17 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 23:01:18 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-28 23:01:18 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 23:01:18 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 23:01:18 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-28 23:01:18 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-28 23:01:18 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-28 23:01:18 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-28 23:01:21 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-28 23:01:21 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-28 23:01:21 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-28 23:01:33 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-28 23:01:39 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-28 23:01:39 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-28 23:01:39 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-28 23:01:45 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-28 23:01:45 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-28 23:01:45 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-28 23:01:52 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-28 23:01:52 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-28 23:01:52 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1026
2025-06-28 23:01:52 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': **********, 'task_type': 'workflow', 'data': {'workflow_id': 'feda07bf-a91e-4004-80cb-72416cdb5a43', 'payload': {'user_dependent_fields': ['iteration_list'], 'user_payload_template': {'iteration_list': {'value': '["blockchain","automation","healthcare"]', 'transition_id': 'LoopNode-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-06-28 23:01:52 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 23:01:52 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 23:01:52 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-28 23:01:52 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow LOOP TEST DO NOT DELETE retrieved successfully",
  "workflow": {
    "id": "feda07bf-a91e-4004-80cb-72416cdb5a43",
    "name": "LOOP TEST DO NOT DELETE",
    "description": "LOOP_TEST_DO_NOT_DELETE",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/16a976d6-6669-4cb4-8207-4f9b8626a0b8.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/91cb74a1-1bfd-42fa-b59d-58773b35d33a.json",
    "start_nodes": [
      {
        "field": "iteration_list",
        "type": "list",
        "transition_id": "transition-LoopNode-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-24T11:13:46.129953",
    "updated_at": "2025-06-28T14:13:59.013729",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "LoopNode",
        "display_name": "For Each Loop",
        "type": "component",
        "transition_id": "transition-LoopNode-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1751052569332"
      },
      {
        "name": "MCP_script-generation-mcp_script_generate",
        "id": "748a8221-d7d9-4352-93ae-00700f4d28b1",
        "transition_id": "transition-MCP_script-generation-mcp_script_generate-*************",
        "type": "mcp",
        "display_name": "script-generation-mcp - script_generate",
        "data": {
          "input_schema": {
            "$defs": {
              "Keywords": {
                "properties": {
                  "time": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Time"
                  },
                  "objective": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Objective"
                  },
                  "audience": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Audience"
                  },
                  "gender": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Gender"
                  },
                  "tone": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Tone"
                  },
                  "speakers": {
                    "anyOf": [
                      {
                        "items": {
                          "type": "string"
                        },
                        "type": "array"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Speakers"
                  }
                },
                "title": "Keywords",
                "type": "object"
              },
              "ScriptType": {
                "enum": [
                  "VIDEO",
                  "TOPIC",
                  "SCRIPT",
                  "BLOG",
                  "AI"
                ],
                "title": "ScriptType",
                "type": "string"
              },
              "VideoType": {
                "enum": [
                  "SHORT",
                  "LONG"
                ],
                "title": "VideoType",
                "type": "string"
              }
            },
            "properties": {
              "topic": {
                "title": "Topic",
                "type": "string"
              },
              "script_type": {
                "$ref": "#/$defs/ScriptType",
                "default": "TOPIC"
              },
              "keywords": {
                "$ref": "#/$defs/Keywords"
              },
              "video_type": {
                "$ref": "#/$defs/VideoType",
                "default": "SHORT"
              },
              "link": {
                "anyOf": [
                  {
                    "format": "uri",
                    "maxLength": 2083,
                    "minLength": 1,
                    "type": "string"
                  },
                  {
                    "type": "null"
                  }
                ],
                "default": null,
                "title": "Link"
              }
            },
            "required": [
              "topic"
            ],
            "title": "GenerateScriptInput",
            "type": "object"
          },
          "output_schema": {
            "properties": {
              "title": {
                "type": "string",
                "description": "Title of the generated script",
                "title": "title"
              },
              "script": {
                "type": "string",
                "description": "The generated script",
                "title": "script"
              },
              "script_type": {
                "type": "string",
                "description": "Type of the script",
                "title": "script_type"
              },
              "video_type": {
                "type": "string",
                "description": "The type of video",
                "title": "video_type"
              },
              "link": {
                "type": "string",
                "format": "uri",
                "description": "Optional link for the script",
                "title": "link"
              }
            }
          }
        }
      }
    ],
    "is_updated": true
  }
}
2025-06-28 23:01:53 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for feda07bf-a91e-4004-80cb-72416cdb5a43 - server_script_path is optional
2025-06-28 23:01:53 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-28 23:01:53 - StateManager - DEBUG - Using global database connections from initializer
2025-06-28 23:01:53 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 23:01:53 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 23:01:53 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 23:01:53 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 23:01:53 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 23:01:53 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-28 23:01:53 - StateManager - DEBUG - Using provided database connections
2025-06-28 23:01:53 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 23:01:53 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 23:01:53 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 23:01:54 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 23:01:54 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 23:01:54 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-28 23:01:54 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751052569332: ['transition-LoopNode-*************']
2025-06-28 23:01:54 - StateManager - INFO - Built dependency map for 3 transitions
2025-06-28 23:01:54 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-28 23:01:54 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751052569332 depends on: ['transition-LoopNode-*************']
2025-06-28 23:01:54 - MCPToolExecutor - DEBUG - Set correlation ID to: 88d87581-dbc2-440b-bc43-d515e1999bca
2025-06-28 23:01:54 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 88d87581-dbc2-440b-bc43-d515e1999bca in tool_executor
2025-06-28 23:01:54 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 23:01:54 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-06-28 23:01:54 - NodeExecutor - DEBUG - Set correlation ID to: 88d87581-dbc2-440b-bc43-d515e1999bca
2025-06-28 23:01:54 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 88d87581-dbc2-440b-bc43-d515e1999bca in node_executor
2025-06-28 23:01:54 - AgentExecutor - DEBUG - Set correlation ID to: 88d87581-dbc2-440b-bc43-d515e1999bca
2025-06-28 23:01:54 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 88d87581-dbc2-440b-bc43-d515e1999bca in agent_executor
2025-06-28 23:01:54 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 23:01:54 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-06-28 23:01:54 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-28 23:01:54 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 88d87581-dbc2-440b-bc43-d515e1999bca
2025-06-28 23:01:54 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 88d87581-dbc2-440b-bc43-d515e1999bca
2025-06-28 23:01:54 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-28 23:01:54 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 23:01:54 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 23:01:54 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 23:01:54 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 23:01:55 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:88d87581-dbc2-440b-bc43-d515e1999bca'
2025-06-28 23:01:55 - RedisManager - DEBUG - Set key 'workflow_state:88d87581-dbc2-440b-bc43-d515e1999bca' with TTL of 600 seconds
2025-06-28 23:01:55 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 88d87581-dbc2-440b-bc43-d515e1999bca. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:01:55 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 23:01:55 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 23:01:55 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 23:01:55 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 23:01:55 - StateManager - INFO - Terminated: False
2025-06-28 23:01:55 - StateManager - INFO - Pending transitions (0): []
2025-06-28 23:01:55 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 23:01:55 - StateManager - INFO - Completed transitions (0): []
2025-06-28 23:01:55 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 23:01:55 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:01:55 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:01:55 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:01:55 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:01:55 - StateManager - INFO - Workflow paused: False
2025-06-28 23:01:55 - StateManager - INFO - ==============================
2025-06-28 23:01:55 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 23:01:55 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:01:55 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-28 23:01:55 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 23:01:55 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-28 23:01:55 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 23:01:55 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 23:01:55 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 23:01:55 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 23:01:55 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 23:01:55 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 23:01:55 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:01:55 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:01:55 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:01:55 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-28 23:01:55 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 23:01:55 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 23:01:55 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 23:01:55 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 23:01:55 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 23:01:55 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 23:01:55 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-28 23:01:55 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-28 23:01:55 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-28 23:01:55 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-28 23:01:55 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-28 23:01:55 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:01:55 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-28 23:01:55 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-28 23:01:55 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-28 23:01:55 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-28 23:01:55 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-28 23:01:55 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-28 23:01:55 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-28 23:01:55 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-28 23:01:55 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:01:55 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-28 23:01:55 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain
2025-06-28 23:01:55 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 23:01:56 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 23:01:56 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 23:01:56 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:01:56 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 23:01:56 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 23:01:56 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 23:01:57 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 23:01:57 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 23:01:57 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:01:57 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 23:01:57 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration'}
2025-06-28 23:01:57 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266616.570111125, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:01:58 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 23:01:58 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 23:01:58 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:01:58 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:01:58 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration', 'transition-LoopNode-*************'}
2025-06-28 23:01:58 - LoopExecutor - DEBUG - 💾 Stored loop iteration data under transition ID: transition-LoopNode-*************
2025-06-28 23:01:58 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-28 23:01:58 - LoopExecutor - DEBUG - 🚀 Starting entry transition: transition-CombineTextComponent-*************
2025-06-28 23:01:58 - LoopExecutor - DEBUG - 🎯 Executing transition transition-CombineTextComponent-************* through orchestration engine
2025-06-28 23:01:58 - LoopExecutor - INFO - 🚀 REAL EXECUTION: Starting transition-CombineTextComponent-************* via transition handler
2025-06-28 23:01:58 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 23:01:58 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:01:58 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 2, 'workflow_status': 'running'}
2025-06-28 23:01:58 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 23:01:58 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 23:01:58 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 23:01:58 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 23:01:58 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 23:01:59 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 23:01:59 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 23:01:59 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266616.570111125, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:01:59 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 23:01:59 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266616.570111125, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-28 23:01:59 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 23:01:59 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 23:01:59 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 23:01:59 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 23:01:59 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 23:01:59 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 23:01:59 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:01:59 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:01:59 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:01:59 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:01:59 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 3, 'workflow_status': 'running'}
2025-06-28 23:01:59 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 999aa5ff-7432-4d65-8b5e-8362a6805ba2) using provided producer.
2025-06-28 23:01:59 - NodeExecutor - DEBUG - Added correlation_id 88d87581-dbc2-440b-bc43-d515e1999bca to payload
2025-06-28 23:01:59 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 23:01:59 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '999aa5ff-7432-4d65-8b5e-8362a6805ba2', 'correlation_id': '88d87581-dbc2-440b-bc43-d515e1999bca', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 23:01:59 - NodeExecutor - DEBUG - Request 999aa5ff-7432-4d65-8b5e-8362a6805ba2 sent successfully using provided producer.
2025-06-28 23:01:59 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 999aa5ff-7432-4d65-8b5e-8362a6805ba2...
2025-06-28 23:01:59 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1026, corr_id: 88d87581-dbc2-440b-bc43-d515e1999bca
2025-06-28 23:02:00 - NodeExecutor - DEBUG - Result consumer received message: Offset=881
2025-06-28 23:02:00 - NodeExecutor - DEBUG - Received valid result for request_id 999aa5ff-7432-4d65-8b5e-8362a6805ba2
2025-06-28 23:02:00 - NodeExecutor - INFO - Result received for request 999aa5ff-7432-4d65-8b5e-8362a6805ba2.
2025-06-28 23:02:00 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-28 23:02:00 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:00 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 4, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 23:02:00 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751131920.1748}}
2025-06-28 23:02:00 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 23:02:00 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 23:02:00 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:00 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:00 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'current_iteration', 'transition-LoopNode-*************'}
2025-06-28 23:02:00 - LoopExecutor - INFO - 🚨 async notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-28 23:02:00 - LoopExecutor - DEBUG - 🔔 Received completion notification for transition: transition-CombineTextComponent-*************
2025-06-28 23:02:00 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:00 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.54 seconds
2025-06-28 23:02:00 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:00 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Completed transition in 2.54 seconds', 'message': 'Transition completed in 2.54 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 5, 'workflow_status': 'running'}
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:00 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-28 23:02:00 - LoopExecutor - DEBUG - 🔗 Adding next transitions to orchestration engine queue: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:00 - StateManager - INFO - Added transitions to pending: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:00 - StateManager - DEBUG - Updated pending transitions: {'transition-MCP_script-generation-mcp_script_generate-*************'}
2025-06-28 23:02:00 - LoopExecutor - DEBUG - 🚀 Creating new orchestration engine execution task to process transitions
2025-06-28 23:02:00 - LoopExecutor - DEBUG - ✅ Started transition chain from transition-CombineTextComponent-*************
2025-06-28 23:02:00 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:00 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:00 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 23:02:00 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 23:02:00 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 23:02:00 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 23:02:01 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:88d87581-dbc2-440b-bc43-d515e1999bca'
2025-06-28 23:02:01 - RedisManager - DEBUG - Set key 'workflow_state:88d87581-dbc2-440b-bc43-d515e1999bca' with TTL of 600 seconds
2025-06-28 23:02:01 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 88d87581-dbc2-440b-bc43-d515e1999bca. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:01 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 23:02:01 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 23:02:01 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 23:02:01 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 23:02:01 - StateManager - INFO - Terminated: False
2025-06-28 23:02:01 - StateManager - INFO - Pending transitions (0): []
2025-06-28 23:02:01 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 23:02:01 - StateManager - INFO - Completed transitions (0): []
2025-06-28 23:02:01 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 23:02:01 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:02:01 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:02:01 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:02:01 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:02:01 - StateManager - INFO - Workflow paused: False
2025-06-28 23:02:01 - StateManager - INFO - ==============================
2025-06-28 23:02:01 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 23:02:01 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:01 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-06-28 23:02:01 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 23:02:01 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-28 23:02:01 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 23:02:01 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 23:02:01 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 23:02:01 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 23:02:01 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 23:02:01 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 23:02:01 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:02:01 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:02:01 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:01 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 7, 'workflow_status': 'running'}
2025-06-28 23:02:01 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 23:02:01 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 23:02:01 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 23:02:01 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 23:02:01 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 23:02:01 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 23:02:01 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-28 23:02:01 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-28 23:02:01 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-28 23:02:01 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-28 23:02:01 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-28 23:02:01 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:02:01 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-28 23:02:01 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-28 23:02:01 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-28 23:02:01 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-28 23:02:01 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-28 23:02:01 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-28 23:02:01 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-28 23:02:01 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-28 23:02:01 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:01 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-28 23:02:01 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain
2025-06-28 23:02:01 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 23:02:01 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 23:02:02 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 23:02:02 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:02 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:02 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 23:02:02 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 23:02:02 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 23:02:03 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 23:02:03 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:03 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:03 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration'}
2025-06-28 23:02:03 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266622.128008625, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:03 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 23:02:03 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 23:02:03 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:03 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:03 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration', 'transition-LoopNode-*************'}
2025-06-28 23:02:03 - LoopExecutor - DEBUG - 💾 Stored loop iteration data under transition ID: transition-LoopNode-*************
2025-06-28 23:02:03 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-28 23:02:03 - LoopExecutor - DEBUG - 🚀 Starting entry transition: transition-CombineTextComponent-*************
2025-06-28 23:02:03 - LoopExecutor - DEBUG - 🎯 Executing transition transition-CombineTextComponent-************* through orchestration engine
2025-06-28 23:02:03 - LoopExecutor - INFO - 🚀 REAL EXECUTION: Starting transition-CombineTextComponent-************* via transition handler
2025-06-28 23:02:03 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 23:02:03 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:03 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 8, 'workflow_status': 'running'}
2025-06-28 23:02:03 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 23:02:03 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 23:02:03 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 23:02:03 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 23:02:03 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 23:02:04 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 23:02:04 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 23:02:04 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266622.128008625, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:02:04 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 23:02:04 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266622.128008625, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-28 23:02:04 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 23:02:04 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 23:02:04 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 23:02:04 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 23:02:04 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 23:02:04 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 23:02:04 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:02:04 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:02:04 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:02:04 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:04 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 9, 'workflow_status': 'running'}
2025-06-28 23:02:04 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: f426ed07-88a3-4605-8265-667e1ebef6b9) using provided producer.
2025-06-28 23:02:04 - NodeExecutor - DEBUG - Added correlation_id 88d87581-dbc2-440b-bc43-d515e1999bca to payload
2025-06-28 23:02:04 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 23:02:04 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': 'f426ed07-88a3-4605-8265-667e1ebef6b9', 'correlation_id': '88d87581-dbc2-440b-bc43-d515e1999bca', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 23:02:04 - NodeExecutor - DEBUG - Request f426ed07-88a3-4605-8265-667e1ebef6b9 sent successfully using provided producer.
2025-06-28 23:02:04 - NodeExecutor - DEBUG - Waiting indefinitely for result for request f426ed07-88a3-4605-8265-667e1ebef6b9...
2025-06-28 23:02:05 - NodeExecutor - DEBUG - Result consumer received message: Offset=882
2025-06-28 23:02:05 - NodeExecutor - DEBUG - Received valid result for request_id f426ed07-88a3-4605-8265-667e1ebef6b9
2025-06-28 23:02:05 - NodeExecutor - INFO - Result received for request f426ed07-88a3-4605-8265-667e1ebef6b9.
2025-06-28 23:02:05 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-28 23:02:05 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 10, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 23:02:05 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751131925.7101922}}
2025-06-28 23:02:06 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 23:02:06 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 23:02:06 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:06 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:06 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'current_iteration', 'transition-LoopNode-*************'}
2025-06-28 23:02:06 - LoopExecutor - INFO - 🚨 async notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-28 23:02:06 - LoopExecutor - DEBUG - 🔔 Received completion notification for transition: transition-CombineTextComponent-*************
2025-06-28 23:02:06 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:06 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.52 seconds
2025-06-28 23:02:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Completed transition in 2.52 seconds', 'message': 'Transition completed in 2.52 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 11, 'workflow_status': 'running'}
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:06 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-28 23:02:06 - LoopExecutor - DEBUG - 🔗 Adding next transitions to orchestration engine queue: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:06 - StateManager - INFO - Added transitions to pending: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:06 - StateManager - DEBUG - Updated pending transitions: {'transition-MCP_script-generation-mcp_script_generate-*************'}
2025-06-28 23:02:06 - LoopExecutor - DEBUG - 🚀 Creating new orchestration engine execution task to process transitions
2025-06-28 23:02:06 - LoopExecutor - DEBUG - ✅ Started transition chain from transition-CombineTextComponent-*************
2025-06-28 23:02:06 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:06 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:06 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 23:02:06 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 23:02:06 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 23:02:06 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 23:02:06 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:88d87581-dbc2-440b-bc43-d515e1999bca'
2025-06-28 23:02:07 - RedisManager - DEBUG - Set key 'workflow_state:88d87581-dbc2-440b-bc43-d515e1999bca' with TTL of 600 seconds
2025-06-28 23:02:07 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 88d87581-dbc2-440b-bc43-d515e1999bca. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:07 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 23:02:07 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 23:02:07 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 23:02:07 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 23:02:07 - StateManager - INFO - Terminated: False
2025-06-28 23:02:07 - StateManager - INFO - Pending transitions (0): []
2025-06-28 23:02:07 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 23:02:07 - StateManager - INFO - Completed transitions (0): []
2025-06-28 23:02:07 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 23:02:07 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:02:07 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:02:07 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:02:07 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:02:07 - StateManager - INFO - Workflow paused: False
2025-06-28 23:02:07 - StateManager - INFO - ==============================
2025-06-28 23:02:07 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 23:02:07 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:07 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 12, 'workflow_status': 'running'}
2025-06-28 23:02:07 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 23:02:07 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-28 23:02:07 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 23:02:07 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 23:02:07 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 23:02:07 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 23:02:07 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 23:02:07 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 23:02:07 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:02:07 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:02:07 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:07 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 13, 'workflow_status': 'running'}
2025-06-28 23:02:07 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 23:02:07 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 23:02:07 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 23:02:07 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 23:02:07 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 23:02:07 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 23:02:07 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-28 23:02:07 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-28 23:02:07 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-28 23:02:07 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-28 23:02:07 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-28 23:02:07 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:02:07 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-28 23:02:07 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-28 23:02:07 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-28 23:02:07 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-28 23:02:07 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-28 23:02:07 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-28 23:02:07 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-28 23:02:07 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-28 23:02:07 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:07 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-28 23:02:07 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain
2025-06-28 23:02:07 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 23:02:07 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 23:02:08 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 23:02:08 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:08 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:08 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 23:02:08 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 23:02:08 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 23:02:08 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 23:02:08 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:08 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:08 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration'}
2025-06-28 23:02:08 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266627.8658855, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:09 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 23:02:09 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 23:02:09 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:09 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:09 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration', 'transition-LoopNode-*************'}
2025-06-28 23:02:09 - LoopExecutor - DEBUG - 💾 Stored loop iteration data under transition ID: transition-LoopNode-*************
2025-06-28 23:02:09 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-28 23:02:09 - LoopExecutor - DEBUG - 🚀 Starting entry transition: transition-CombineTextComponent-*************
2025-06-28 23:02:09 - LoopExecutor - DEBUG - 🎯 Executing transition transition-CombineTextComponent-************* through orchestration engine
2025-06-28 23:02:09 - LoopExecutor - INFO - 🚀 REAL EXECUTION: Starting transition-CombineTextComponent-************* via transition handler
2025-06-28 23:02:09 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 23:02:09 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:09 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 14, 'workflow_status': 'running'}
2025-06-28 23:02:09 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 23:02:09 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 23:02:09 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 23:02:09 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 23:02:09 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 23:02:10 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 23:02:10 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 23:02:10 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266627.8658855, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:02:10 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 23:02:10 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266627.8658855, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-28 23:02:10 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 23:02:10 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 23:02:10 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 23:02:10 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 23:02:10 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 23:02:10 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 23:02:10 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:02:10 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:02:10 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:02:10 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:10 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 15, 'workflow_status': 'running'}
2025-06-28 23:02:10 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 499f500d-aa6e-4741-b022-84ec10a1b446) using provided producer.
2025-06-28 23:02:10 - NodeExecutor - DEBUG - Added correlation_id 88d87581-dbc2-440b-bc43-d515e1999bca to payload
2025-06-28 23:02:10 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 23:02:10 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '499f500d-aa6e-4741-b022-84ec10a1b446', 'correlation_id': '88d87581-dbc2-440b-bc43-d515e1999bca', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 23:02:10 - NodeExecutor - DEBUG - Request 499f500d-aa6e-4741-b022-84ec10a1b446 sent successfully using provided producer.
2025-06-28 23:02:10 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 499f500d-aa6e-4741-b022-84ec10a1b446...
2025-06-28 23:02:11 - NodeExecutor - DEBUG - Result consumer received message: Offset=883
2025-06-28 23:02:11 - NodeExecutor - DEBUG - Received valid result for request_id 499f500d-aa6e-4741-b022-84ec10a1b446
2025-06-28 23:02:11 - NodeExecutor - INFO - Result received for request 499f500d-aa6e-4741-b022-84ec10a1b446.
2025-06-28 23:02:11 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-28 23:02:11 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:11 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 16, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 23:02:11 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751131931.46782}}
2025-06-28 23:02:11 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 23:02:12 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 23:02:12 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:12 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:12 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'current_iteration', 'transition-LoopNode-*************'}
2025-06-28 23:02:12 - LoopExecutor - INFO - 🚨 async notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-28 23:02:12 - LoopExecutor - DEBUG - 🔔 Received completion notification for transition: transition-CombineTextComponent-*************
2025-06-28 23:02:12 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:12 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.52 seconds
2025-06-28 23:02:12 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:12 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Completed transition in 2.52 seconds', 'message': 'Transition completed in 2.52 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 17, 'workflow_status': 'running'}
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:12 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-28 23:02:12 - LoopExecutor - DEBUG - 🔗 Adding next transitions to orchestration engine queue: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:12 - StateManager - INFO - Added transitions to pending: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:12 - StateManager - DEBUG - Updated pending transitions: {'transition-MCP_script-generation-mcp_script_generate-*************'}
2025-06-28 23:02:12 - LoopExecutor - DEBUG - 🚀 Creating new orchestration engine execution task to process transitions
2025-06-28 23:02:12 - LoopExecutor - DEBUG - ✅ Started transition chain from transition-CombineTextComponent-*************
2025-06-28 23:02:12 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:12 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:12 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 23:02:12 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 23:02:12 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 23:02:12 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 23:02:12 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:88d87581-dbc2-440b-bc43-d515e1999bca'
2025-06-28 23:02:13 - RedisManager - DEBUG - Set key 'workflow_state:88d87581-dbc2-440b-bc43-d515e1999bca' with TTL of 600 seconds
2025-06-28 23:02:13 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 88d87581-dbc2-440b-bc43-d515e1999bca. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:13 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 23:02:13 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 23:02:13 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 23:02:13 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 23:02:13 - StateManager - INFO - Terminated: False
2025-06-28 23:02:13 - StateManager - INFO - Pending transitions (0): []
2025-06-28 23:02:13 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 23:02:13 - StateManager - INFO - Completed transitions (0): []
2025-06-28 23:02:13 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 23:02:13 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:02:13 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:02:13 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:02:13 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:02:13 - StateManager - INFO - Workflow paused: False
2025-06-28 23:02:13 - StateManager - INFO - ==============================
2025-06-28 23:02:13 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 23:02:13 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:13 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 18, 'workflow_status': 'running'}
2025-06-28 23:02:13 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 23:02:13 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-28 23:02:13 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 23:02:13 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 23:02:13 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 23:02:13 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 23:02:13 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 23:02:13 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 23:02:13 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:02:13 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:02:13 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:13 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 19, 'workflow_status': 'running'}
2025-06-28 23:02:13 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 23:02:13 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 23:02:13 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 23:02:13 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 23:02:13 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 23:02:13 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 23:02:13 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-28 23:02:13 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-28 23:02:13 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-28 23:02:13 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-28 23:02:13 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-28 23:02:13 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:02:13 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-28 23:02:13 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-28 23:02:13 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-28 23:02:13 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-28 23:02:13 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-28 23:02:13 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-28 23:02:13 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-28 23:02:13 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-28 23:02:13 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:13 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-28 23:02:13 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain
2025-06-28 23:02:13 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 23:02:13 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 23:02:13 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 23:02:13 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:13 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:13 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 23:02:13 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 23:02:14 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 23:02:14 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 23:02:14 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 23:02:14 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:14 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:14 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration'}
2025-06-28 23:02:14 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266633.543426541, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:14 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 23:02:15 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 23:02:15 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 23:02:15 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 23:02:15 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 23:02:15 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:15 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:15 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration', 'transition-LoopNode-*************'}
2025-06-28 23:02:15 - LoopExecutor - DEBUG - 💾 Stored loop iteration data under transition ID: transition-LoopNode-*************
2025-06-28 23:02:15 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-28 23:02:15 - LoopExecutor - DEBUG - 🚀 Starting entry transition: transition-CombineTextComponent-*************
2025-06-28 23:02:15 - LoopExecutor - DEBUG - 🎯 Executing transition transition-CombineTextComponent-************* through orchestration engine
2025-06-28 23:02:15 - LoopExecutor - INFO - 🚀 REAL EXECUTION: Starting transition-CombineTextComponent-************* via transition handler
2025-06-28 23:02:15 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 23:02:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 20, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 20, 'workflow_status': 'running'}
2025-06-28 23:02:15 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 23:02:15 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 23:02:15 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 23:02:15 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 23:02:15 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 23:02:16 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 23:02:16 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 23:02:16 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266633.543426541, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:02:16 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 23:02:16 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266633.543426541, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-28 23:02:16 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 23:02:16 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 23:02:16 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 23:02:16 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 23:02:16 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 23:02:16 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 23:02:16 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:02:16 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:02:16 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:02:16 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 21, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:16 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 21, 'workflow_status': 'running'}
2025-06-28 23:02:16 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 16208357-46cd-4d81-bf6a-c8b176bbf72e) using provided producer.
2025-06-28 23:02:16 - NodeExecutor - DEBUG - Added correlation_id 88d87581-dbc2-440b-bc43-d515e1999bca to payload
2025-06-28 23:02:16 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 23:02:16 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '16208357-46cd-4d81-bf6a-c8b176bbf72e', 'correlation_id': '88d87581-dbc2-440b-bc43-d515e1999bca', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 23:02:16 - NodeExecutor - DEBUG - Request 16208357-46cd-4d81-bf6a-c8b176bbf72e sent successfully using provided producer.
2025-06-28 23:02:16 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 16208357-46cd-4d81-bf6a-c8b176bbf72e...
2025-06-28 23:02:17 - NodeExecutor - DEBUG - Result consumer received message: Offset=884
2025-06-28 23:02:17 - NodeExecutor - DEBUG - Received valid result for request_id 16208357-46cd-4d81-bf6a-c8b176bbf72e
2025-06-28 23:02:17 - NodeExecutor - INFO - Result received for request 16208357-46cd-4d81-bf6a-c8b176bbf72e.
2025-06-28 23:02:17 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-28 23:02:17 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 22, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:17 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 22, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 23:02:17 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751131937.203507}}
2025-06-28 23:02:17 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 23:02:17 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 23:02:17 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:17 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:17 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'current_iteration', 'transition-LoopNode-*************'}
2025-06-28 23:02:17 - LoopExecutor - INFO - 🚨 async notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-28 23:02:17 - LoopExecutor - DEBUG - 🔔 Received completion notification for transition: transition-CombineTextComponent-*************
2025-06-28 23:02:17 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:17 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.51 seconds
2025-06-28 23:02:17 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 23, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:17 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Completed transition in 2.51 seconds', 'message': 'Transition completed in 2.51 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 23, 'workflow_status': 'running'}
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:17 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-28 23:02:17 - LoopExecutor - DEBUG - 🔗 Adding next transitions to orchestration engine queue: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:17 - StateManager - INFO - Added transitions to pending: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:17 - StateManager - DEBUG - Updated pending transitions: {'transition-MCP_script-generation-mcp_script_generate-*************'}
2025-06-28 23:02:17 - LoopExecutor - DEBUG - 🚀 Creating new orchestration engine execution task to process transitions
2025-06-28 23:02:17 - LoopExecutor - DEBUG - ✅ Started transition chain from transition-CombineTextComponent-*************
2025-06-28 23:02:17 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:17 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:17 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 23:02:17 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 23:02:17 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 23:02:17 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 23:02:18 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:88d87581-dbc2-440b-bc43-d515e1999bca'
2025-06-28 23:02:18 - RedisManager - DEBUG - Set key 'workflow_state:88d87581-dbc2-440b-bc43-d515e1999bca' with TTL of 600 seconds
2025-06-28 23:02:18 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 88d87581-dbc2-440b-bc43-d515e1999bca. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:18 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 23:02:18 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 23:02:18 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 23:02:18 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 23:02:18 - StateManager - INFO - Terminated: False
2025-06-28 23:02:18 - StateManager - INFO - Pending transitions (0): []
2025-06-28 23:02:18 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 23:02:18 - StateManager - INFO - Completed transitions (0): []
2025-06-28 23:02:18 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 23:02:18 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:02:18 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:02:18 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:02:18 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:02:18 - StateManager - INFO - Workflow paused: False
2025-06-28 23:02:18 - StateManager - INFO - ==============================
2025-06-28 23:02:18 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 23:02:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 24, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 24, 'workflow_status': 'running'}
2025-06-28 23:02:18 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 23:02:18 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-28 23:02:18 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 23:02:18 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 23:02:18 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 23:02:18 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 23:02:18 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 23:02:18 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 23:02:18 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:02:18 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:02:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 25, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 25, 'workflow_status': 'running'}
2025-06-28 23:02:18 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 23:02:18 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 23:02:18 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 23:02:18 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 23:02:18 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 23:02:18 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 23:02:18 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-28 23:02:18 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-28 23:02:18 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-28 23:02:18 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-28 23:02:18 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-28 23:02:18 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:02:18 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-28 23:02:18 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-28 23:02:18 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-28 23:02:18 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-28 23:02:18 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-28 23:02:18 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-28 23:02:18 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-28 23:02:18 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-28 23:02:18 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:18 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-28 23:02:18 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain
2025-06-28 23:02:18 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 23:02:19 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 23:02:19 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 23:02:19 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:19 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:19 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 23:02:19 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 23:02:19 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 23:02:20 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 23:02:20 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:20 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:20 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration'}
2025-06-28 23:02:20 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266639.32810675, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:20 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 23:02:21 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 23:02:21 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:21 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:21 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration', 'transition-LoopNode-*************'}
2025-06-28 23:02:21 - LoopExecutor - DEBUG - 💾 Stored loop iteration data under transition ID: transition-LoopNode-*************
2025-06-28 23:02:21 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-28 23:02:21 - LoopExecutor - DEBUG - 🚀 Starting entry transition: transition-CombineTextComponent-*************
2025-06-28 23:02:21 - LoopExecutor - DEBUG - 🎯 Executing transition transition-CombineTextComponent-************* through orchestration engine
2025-06-28 23:02:21 - LoopExecutor - INFO - 🚀 REAL EXECUTION: Starting transition-CombineTextComponent-************* via transition handler
2025-06-28 23:02:21 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 23:02:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 26, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 26, 'workflow_status': 'running'}
2025-06-28 23:02:21 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 23:02:21 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 23:02:21 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 23:02:21 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 23:02:21 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 23:02:21 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 23:02:21 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 23:02:21 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266639.32810675, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:02:21 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 23:02:21 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266639.32810675, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-28 23:02:21 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 23:02:21 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 23:02:21 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 23:02:21 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 23:02:21 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 23:02:21 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 23:02:21 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:02:21 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:02:21 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:02:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 27, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 27, 'workflow_status': 'running'}
2025-06-28 23:02:21 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 25e2b811-7e78-4bbd-b819-a7c5dd0c425b) using provided producer.
2025-06-28 23:02:21 - NodeExecutor - DEBUG - Added correlation_id 88d87581-dbc2-440b-bc43-d515e1999bca to payload
2025-06-28 23:02:21 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 23:02:21 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '25e2b811-7e78-4bbd-b819-a7c5dd0c425b', 'correlation_id': '88d87581-dbc2-440b-bc43-d515e1999bca', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 23:02:21 - NodeExecutor - DEBUG - Request 25e2b811-7e78-4bbd-b819-a7c5dd0c425b sent successfully using provided producer.
2025-06-28 23:02:21 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 25e2b811-7e78-4bbd-b819-a7c5dd0c425b...
2025-06-28 23:02:22 - NodeExecutor - DEBUG - Result consumer received message: Offset=885
2025-06-28 23:02:22 - NodeExecutor - DEBUG - Received valid result for request_id 25e2b811-7e78-4bbd-b819-a7c5dd0c425b
2025-06-28 23:02:22 - NodeExecutor - INFO - Result received for request 25e2b811-7e78-4bbd-b819-a7c5dd0c425b.
2025-06-28 23:02:22 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-28 23:02:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 28, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 28, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 23:02:22 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751131942.984319}}
2025-06-28 23:02:23 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 23:02:23 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 23:02:23 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:23 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:23 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'current_iteration', 'transition-LoopNode-*************'}
2025-06-28 23:02:23 - LoopExecutor - INFO - 🚨 async notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-28 23:02:23 - LoopExecutor - DEBUG - 🔔 Received completion notification for transition: transition-CombineTextComponent-*************
2025-06-28 23:02:23 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:23 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.59 seconds
2025-06-28 23:02:23 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 29, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:23 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Completed transition in 2.59 seconds', 'message': 'Transition completed in 2.59 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 29, 'workflow_status': 'running'}
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:23 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-28 23:02:23 - LoopExecutor - DEBUG - 🔗 Adding next transitions to orchestration engine queue: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:23 - StateManager - INFO - Added transitions to pending: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:23 - StateManager - DEBUG - Updated pending transitions: {'transition-MCP_script-generation-mcp_script_generate-*************'}
2025-06-28 23:02:23 - LoopExecutor - DEBUG - 🚀 Creating new orchestration engine execution task to process transitions
2025-06-28 23:02:23 - LoopExecutor - DEBUG - ✅ Started transition chain from transition-CombineTextComponent-*************
2025-06-28 23:02:23 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:23 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:23 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 23:02:23 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 23:02:23 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 23:02:23 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 23:02:24 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:88d87581-dbc2-440b-bc43-d515e1999bca'
2025-06-28 23:02:24 - RedisManager - DEBUG - Set key 'workflow_state:88d87581-dbc2-440b-bc43-d515e1999bca' with TTL of 600 seconds
2025-06-28 23:02:24 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 88d87581-dbc2-440b-bc43-d515e1999bca. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:24 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 23:02:24 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 23:02:24 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 23:02:24 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 23:02:24 - StateManager - INFO - Terminated: False
2025-06-28 23:02:24 - StateManager - INFO - Pending transitions (0): []
2025-06-28 23:02:24 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 23:02:24 - StateManager - INFO - Completed transitions (0): []
2025-06-28 23:02:24 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 23:02:24 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:02:24 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:02:24 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:02:24 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:02:24 - StateManager - INFO - Workflow paused: False
2025-06-28 23:02:24 - StateManager - INFO - ==============================
2025-06-28 23:02:24 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 23:02:24 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 30, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 30, 'workflow_status': 'running'}
2025-06-28 23:02:24 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 23:02:24 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-28 23:02:24 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 23:02:24 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 23:02:24 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 23:02:24 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 23:02:24 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 23:02:24 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 23:02:24 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:02:24 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:02:24 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 31, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 31, 'workflow_status': 'running'}
2025-06-28 23:02:24 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 23:02:24 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 23:02:24 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 23:02:24 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 23:02:24 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 23:02:24 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 23:02:24 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-28 23:02:24 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-28 23:02:24 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-28 23:02:24 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-28 23:02:24 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-28 23:02:24 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:02:24 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-28 23:02:24 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-28 23:02:24 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-28 23:02:24 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-28 23:02:24 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-28 23:02:24 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-28 23:02:24 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-28 23:02:24 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-28 23:02:24 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:24 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-28 23:02:24 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain
2025-06-28 23:02:24 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 23:02:24 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 23:02:25 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 23:02:25 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:25 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:25 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 23:02:25 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 23:02:25 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 23:02:26 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 23:02:26 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:26 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:26 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration'}
2025-06-28 23:02:26 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266645.076999166, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:26 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-28 23:02:26 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-28 23:02:26 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:26 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:26 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration', 'transition-LoopNode-*************'}
2025-06-28 23:02:26 - LoopExecutor - DEBUG - 💾 Stored loop iteration data under transition ID: transition-LoopNode-*************
2025-06-28 23:02:26 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-28 23:02:26 - LoopExecutor - DEBUG - 🚀 Starting entry transition: transition-CombineTextComponent-*************
2025-06-28 23:02:26 - LoopExecutor - DEBUG - 🎯 Executing transition transition-CombineTextComponent-************* through orchestration engine
2025-06-28 23:02:26 - LoopExecutor - INFO - 🚀 REAL EXECUTION: Starting transition-CombineTextComponent-************* via transition handler
2025-06-28 23:02:26 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-28 23:02:26 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 32, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:26 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 32, 'workflow_status': 'running'}
2025-06-28 23:02:26 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-28 23:02:26 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-28 23:02:26 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-28 23:02:26 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-28 23:02:26 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-28 23:02:27 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-28 23:02:27 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-28 23:02:27 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266645.076999166, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:02:27 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-28 23:02:27 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266645.076999166, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': blockchain
2025-06-28 23:02:27 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-28 23:02:27 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - Filtering out field 'separator' with null/empty value: 
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-28 23:02:27 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-28 23:02:27 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-28 23:02:27 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-28 23:02:27 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = and AI
2025-06-28 23:02:27 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:02:27 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:02:27 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}
2025-06-28 23:02:27 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 33, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:27 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 33, 'workflow_status': 'running'}
2025-06-28 23:02:27 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 5ab9d979-3585-486a-83e9-0aa74affc19f) using provided producer.
2025-06-28 23:02:27 - NodeExecutor - DEBUG - Added correlation_id 88d87581-dbc2-440b-bc43-d515e1999bca to payload
2025-06-28 23:02:27 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-28 23:02:27 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'blockchain', 'num_additional_inputs': '1', 'input_1': 'and AI'}, 'request_id': '5ab9d979-3585-486a-83e9-0aa74affc19f', 'correlation_id': '88d87581-dbc2-440b-bc43-d515e1999bca', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-28 23:02:27 - NodeExecutor - DEBUG - Request 5ab9d979-3585-486a-83e9-0aa74affc19f sent successfully using provided producer.
2025-06-28 23:02:27 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 5ab9d979-3585-486a-83e9-0aa74affc19f...
2025-06-28 23:02:28 - NodeExecutor - DEBUG - Result consumer received message: Offset=886
2025-06-28 23:02:28 - NodeExecutor - DEBUG - Received valid result for request_id 5ab9d979-3585-486a-83e9-0aa74affc19f
2025-06-28 23:02:28 - NodeExecutor - INFO - Result received for request 5ab9d979-3585-486a-83e9-0aa74affc19f.
2025-06-28 23:02:28 - TransitionHandler - INFO - Execution result from Components executor: "blockchain and AI"
2025-06-28 23:02:28 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 34, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:28 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition Result received.', 'result': 'blockchain and AI', 'status': 'completed', 'sequence': 34, 'workflow_status': 'running', 'approval_required': False}
2025-06-28 23:02:28 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'blockchain and AI'}, 'status': 'completed', 'timestamp': 1751131948.644133}}
2025-06-28 23:02:29 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-28 23:02:29 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-28 23:02:29 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:29 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:29 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-CombineTextComponent-*************', 'loop_iteration_0', 'current_iteration', 'transition-LoopNode-*************'}
2025-06-28 23:02:29 - LoopExecutor - INFO - 🚨 async notify_transition_completion called for transition transition-CombineTextComponent-*************
2025-06-28 23:02:29 - LoopExecutor - DEBUG - 🔔 Received completion notification for transition: transition-CombineTextComponent-*************
2025-06-28 23:02:29 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-CombineTextComponent-*************
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-MCP_script-generation-mcp_script_generate-*************
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:29 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.50 seconds
2025-06-28 23:02:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 35, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Completed transition in 2.50 seconds', 'message': 'Transition completed in 2.50 seconds', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'time_logged', 'sequence': 35, 'workflow_status': 'running'}
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-CombineTextComponent-*************: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:29 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-28 23:02:29 - LoopExecutor - DEBUG - 🔗 Adding next transitions to orchestration engine queue: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:29 - StateManager - INFO - Added transitions to pending: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:29 - StateManager - DEBUG - Updated pending transitions: {'transition-MCP_script-generation-mcp_script_generate-*************'}
2025-06-28 23:02:29 - LoopExecutor - DEBUG - 🚀 Creating new orchestration engine execution task to process transitions
2025-06-28 23:02:29 - LoopExecutor - DEBUG - ✅ Started transition chain from transition-CombineTextComponent-*************
2025-06-28 23:02:29 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:29 - LoopExecutor - DEBUG - ⏳ Waiting for exit transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:29 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-28 23:02:29 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-28 23:02:29 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-28 23:02:29 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-28 23:02:29 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:88d87581-dbc2-440b-bc43-d515e1999bca'
2025-06-28 23:02:30 - RedisManager - DEBUG - Set key 'workflow_state:88d87581-dbc2-440b-bc43-d515e1999bca' with TTL of 600 seconds
2025-06-28 23:02:30 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 88d87581-dbc2-440b-bc43-d515e1999bca. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:30 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 23:02:30 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 23:02:30 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-28 23:02:30 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 23:02:30 - StateManager - INFO - Terminated: False
2025-06-28 23:02:30 - StateManager - INFO - Pending transitions (0): []
2025-06-28 23:02:30 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 23:02:30 - StateManager - INFO - Completed transitions (0): []
2025-06-28 23:02:30 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 23:02:30 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:02:30 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:02:30 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 23:02:30 - StateManager - INFO - Workflow status: inactive
2025-06-28 23:02:30 - StateManager - INFO - Workflow paused: False
2025-06-28 23:02:30 - StateManager - INFO - ==============================
2025-06-28 23:02:30 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-28 23:02:30 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 36, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 36, 'workflow_status': 'running'}
2025-06-28 23:02:30 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-28 23:02:30 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-28 23:02:30 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 23:02:30 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 23:02:30 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-28 23:02:30 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 23:02:30 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 23:02:30 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 23:02:30 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:02:30 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 23:02:30 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 37, corr_id 88d87581-dbc2-440b-bc43-d515e1999bca):
2025-06-28 23:02:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 37, 'workflow_status': 'running'}
2025-06-28 23:02:30 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-28 23:02:30 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-28 23:02:30 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ['blockchain', 'automation', 'healthcare']
2025-06-28 23:02:30 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-28 23:02:30 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 23:02:30 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-28 23:02:30 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-28 23:02:30 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-28 23:02:30 - LoopExecutor - DEBUG - 📋 Detected iteration_list with 3 items, batch_size: 1
2025-06-28 23:02:30 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list', 'include_metadata': True}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'iteration_source': {'type': 'list', 'data': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1, 'original': {'iteration_list': ['blockchain', 'automation', 'healthcare'], 'batch_size': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-28 23:02:30 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-28 23:02:30 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:02:30 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-28 23:02:30 - LoopExecutor - INFO - Prepared 3 iterations from list source
2025-06-28 23:02:30 - LoopExecutor - INFO - 🔄 Initialized loop state for 3 iterations
2025-06-28 23:02:30 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-28 23:02:30 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-28 23:02:30 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-28 23:02:30 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-MCP_script-generation-mcp_script_generate-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-28 23:02:30 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-28 23:02:30 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-MCP_script-generation-mcp_script_generate-*************']
2025-06-28 23:02:30 - LoopExecutor - INFO - 🔄 Starting iteration 1/3
2025-06-28 23:02:30 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain
2025-06-28 23:02:30 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: blockchain
2025-06-28 23:02:30 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-28 23:02:31 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-28 23:02:31 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:31 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:31 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-28 23:02:31 - StateManager - DEBUG - Stored result for transition current_iteration in memory: blockchain
2025-06-28 23:02:31 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-28 23:02:31 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-28 23:02:31 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 23:02:31 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-28 23:02:31 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration'}
2025-06-28 23:02:31 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'blockchain', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 266650.734568291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-28 23:02:32 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-28 23:02:32 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-28 23:02:32 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-28 23:02:32 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-28 23:02:32 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-28 23:02:32 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-28 23:02:32 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-28 23:02:32 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-28 23:02:32 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-28 23:02:32 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-28 23:02:32 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-28 23:02:32 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-28 23:02:32 - EnhancedWorkflowEngine - WARNING - Workflow 88d87581-dbc2-440b-bc43-d515e1999bca execution was cancelled!
2025-06-28 23:02:32 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 23:02:32 - EnhancedWorkflowEngine - WARNING - Workflow 88d87581-dbc2-440b-bc43-d515e1999bca execution was cancelled!
2025-06-28 23:02:32 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 23:02:32 - EnhancedWorkflowEngine - WARNING - Workflow 88d87581-dbc2-440b-bc43-d515e1999bca execution was cancelled!
2025-06-28 23:02:32 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 23:02:32 - EnhancedWorkflowEngine - WARNING - Workflow 88d87581-dbc2-440b-bc43-d515e1999bca execution was cancelled!
2025-06-28 23:02:32 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 23:02:32 - EnhancedWorkflowEngine - WARNING - Workflow 88d87581-dbc2-440b-bc43-d515e1999bca execution was cancelled!
2025-06-28 23:02:32 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 23:02:32 - EnhancedWorkflowEngine - WARNING - Workflow 88d87581-dbc2-440b-bc43-d515e1999bca execution was cancelled!
2025-06-28 23:02:32 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 23:02:32 - EnhancedWorkflowEngine - WARNING - Workflow 88d87581-dbc2-440b-bc43-d515e1999bca execution was cancelled!
2025-06-28 23:02:32 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-28 23:02:32 - KafkaWorkflowConsumer - WARNING - Workflow execution for 'feda07bf-a91e-4004-80cb-72416cdb5a43' was cancelled
2025-06-28 23:02:32 - KafkaWorkflowConsumer - INFO - Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' final status: cancelled, result: Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' execution was cancelled
2025-06-28 23:02:32 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 88d87581-dbc2-440b-bc43-d515e1999bca, response: {'status': 'Workflow Cancelled', 'result': "Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' execution was cancelled", 'workflow_status': 'cancelled'}
2025-06-28 23:02:32 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 88d87581-dbc2-440b-bc43-d515e1999bca 
2025-06-28 23:02:32 - Main - ERROR - Shutting down due to keyboard interrupt...
