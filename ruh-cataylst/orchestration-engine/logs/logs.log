2025-06-28 18:30:42 - Main - INFO - Starting Server
2025-06-28 18:30:42 - Main - INFO - Connection at: **************:9092
2025-06-28 18:30:42 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-28 18:30:42 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-28 18:30:42 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-28 18:30:42 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-28 18:30:42 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 18:30:43 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 18:30:43 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 18:30:45 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 18:30:48 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-28 18:30:48 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-28 18:30:50 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-28 18:30:51 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-28 18:30:51 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-28 18:30:52 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-28 18:30:52 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-28 18:30:54 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-28 18:30:54 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-28 18:30:54 - RedisEventListener - INFO - Redis event listener started
2025-06-28 18:30:54 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-28 18:30:54 - StateManager - DEBUG - Using provided database connections
2025-06-28 18:30:54 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 18:30:54 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 18:30:54 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 18:30:55 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-28 18:30:55 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 18:30:55 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 18:30:55 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-28 18:30:55 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-28 18:30:55 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-28 18:30:55 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-28 18:30:59 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-28 18:30:59 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-28 18:30:59 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-28 18:31:03 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-28 18:31:09 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-28 18:31:09 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-28 18:31:09 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-28 18:31:16 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-28 18:31:16 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-28 18:31:16 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-28 18:31:22 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-28 18:31:22 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23883
2025-06-28 18:31:23 - AgentExecutor - ERROR - Error processing result message: 'NoneType' object has no attribute 'get'
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/agent_executor.py", line 165, in _consume_loop
    result_data = result_payload.get("agent_response").get("content")
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get'
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23884
2025-06-28 18:31:23 - AgentExecutor - ERROR - Error processing result message: 'NoneType' object has no attribute 'get'
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/agent_executor.py", line 165, in _consume_loop
    result_data = result_payload.get("agent_response").get("content")
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get'
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23885
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_563dff3d-bfd7-4ea7-9619-c4ae62062b45_2db49a84-9f9f-4005-93e8-4501199c21db', 'session_id': '563dff3d-bfd7-4ea7-9619-c4ae62062b45', 'event_type': 'message_streaming', 'agent_response': {'content': 'Hello! Can you help me analyze this data?', 'source': 'user', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'text', 'metadata': {}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23886
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_563dff3d-bfd7-4ea7-9619-c4ae62062b45_2db49a84-9f9f-4005-93e8-4501199c21db', 'session_id': '563dff3d-bfd7-4ea7-9619-c4ae62062b45', 'event_type': 'message_streaming', 'agent_response': {'content': 'Memory query: ', 'source': 'OrchestratorEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'agent_event', 'metadata': {'event_type': 'memory_query', 'query': '', 'memory_type': '', 'results_count': 0}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23887
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_563dff3d-bfd7-4ea7-9619-c4ae62062b45_2db49a84-9f9f-4005-93e8-4501199c21db', 'session_id': '563dff3d-bfd7-4ea7-9619-c4ae62062b45', 'event_type': 'message_streaming', 'agent_response': {'content': "Your request is a bit ambiguous; to orchestrate an effective workflow, I need a clearer understanding of the data set and your specific analysis goals.\n\nCould you please provide the data you'd like me to analyze (by attaching or pasting it here)? Additionally, let me know:\n- What type of analysis are you interested in (trends, summary, comparison, anomaly detection, etc.)?\n- Are there specific questions or objectives you want addressed?\n- What is the data format (spreadsheet, text, database, etc.)?\n\nThese details will allow me to plan the most suitable workflow and ensure a high-quality analysis.", 'source': 'OrchestratorEmployee', 'models_usage': {'prompt_tokens': 2257, 'completion_tokens': 139}, 'message_type': 'structured_message', 'metadata': {'content': "Your request is a bit ambiguous; to orchestrate an effective workflow, I need a clearer understanding of the data set and your specific analysis goals.\n\nCould you please provide the data you'd like me to analyze (by attaching or pasting it here)? Additionally, let me know:\n- What type of analysis are you interested in (trends, summary, comparison, anomaly detection, etc.)?\n- Are there specific questions or objectives you want addressed?\n- What is the data format (spreadsheet, text, database, etc.)?\n\nThese details will allow me to plan the most suitable workflow and ensure a high-quality analysis.", 'content_type': 'response'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23888
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_563dff3d-bfd7-4ea7-9619-c4ae62062b45_2db49a84-9f9f-4005-93e8-4501199c21db', 'session_id': '563dff3d-bfd7-4ea7-9619-c4ae62062b45', 'event_type': 'message_streaming', 'agent_response': {'content': '', 'source': 'UserProxyAgent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'user_input_request', 'metadata': {'user_input_requested': True, 'event_type': 'user_input_request', 'prompt': '', 'team_conversation_id': 'c8dc2554-49d5-45ef-b732-f3a46ae34887'}}, 'success': True, 'message': 'Human input requested by the team', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23889
2025-06-28 18:31:23 - AgentExecutor - ERROR - Error processing result message: 'NoneType' object has no attribute 'get'
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/agent_executor.py", line 165, in _consume_loop
    result_data = result_payload.get("agent_response").get("content")
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get'
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23890
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'what is your name?', 'source': 'user', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'text', 'metadata': {}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23891
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'Memory query: ', 'source': 'OrchestratorEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'agent_event', 'metadata': {'event_type': 'memory_query', 'query': '', 'memory_type': '', 'results_count': 0}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23892
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'My name is Ruh Master. I am a strategic workflow orchestrator designed to coordinate complex, multi-employee task execution and provide high-quality responses to user queries.', 'source': 'OrchestratorEmployee', 'models_usage': {'prompt_tokens': 2414, 'completion_tokens': 40}, 'message_type': 'structured_message', 'metadata': {'content': 'My name is Ruh Master. I am a strategic workflow orchestrator designed to coordinate complex, multi-employee task execution and provide high-quality responses to user queries.', 'content_type': 'response'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23893
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '##', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23894
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Direct', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23895
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Answer', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23896
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '\n', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23897
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'My', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23898
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' name', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23899
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' is', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23900
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Ruh', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23901
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Master', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23902
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '.', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23903
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' I', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23904
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' am', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23905
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' a', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23906
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' specialized', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23907
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' agent', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23908
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' focused', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23909
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' on', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23910
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' strategic', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23911
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' workflow', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23912
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' orches', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23913
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'tration', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23914
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ',', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23915
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' including', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23916
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' synthes', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23917
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'izing', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23918
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' and', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23919
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' summar', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23920
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'izing', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23921
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' information', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23922
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' to', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23923
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' deliver', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23924
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' comprehensive', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23925
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ',', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23926
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' user', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23927
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '-friendly', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23928
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' reports', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23929
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '.\n\n', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23930
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '##', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23931
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Key', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23932
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Findings', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23933
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '/Main', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23934
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Points', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23935
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '\n', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23936
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '-', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23937
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' **', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23938
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'R', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23939
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'uh', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23940
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Master', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23941
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '**', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23942
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' is', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23943
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' the', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23944
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' designated', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23945
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' name', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23946
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' for', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23947
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' this', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23948
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' orchestr', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23949
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'ator', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23950
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' and', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23951
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' summar', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23952
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'ization', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23953
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' role', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23954
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '.\n', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23955
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '-', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23956
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' The', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23957
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' primary', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23958
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' function', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23959
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' is', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23960
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' to', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23961
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' manage', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23962
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ',', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23963
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' coordinate', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23964
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ',', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23965
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' and', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23966
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' synth', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23967
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'esize', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23968
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' responses', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23969
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' in', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23970
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' a', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23971
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' clear', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23972
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ',', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23973
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' structured', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23974
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' manner', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23975
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '.\n', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23976
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '-', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23977
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' All', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23978
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' outputs', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23979
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' are', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23980
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' formatted', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23981
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' for', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23982
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' professionalism', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23983
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' and', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23984
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' clarity', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23985
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ',', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23986
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' addressing', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23987
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' user', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23988
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' needs', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23989
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' directly', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23990
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '.\n\n', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23991
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '##', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23992
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Detailed', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23993
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Analysis', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23994
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '\n\n', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23995
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '###', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23996
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Role', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23997
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' and', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23998
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Function', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=23999
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '\n', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24000
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '-', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24001
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' **', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24002
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'R', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24003
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'uh', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24004
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Master', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24005
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '**', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24006
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' acts', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24007
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' as', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24008
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' a', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24009
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' central', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24010
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' point', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24011
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' for', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24012
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' integrating', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24013
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' and', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24014
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' summar', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24015
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'izing', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24016
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' information', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24017
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' from', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24018
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' various', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24019
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' agents', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24020
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' or', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24021
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' sources', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24022
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '.\n', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24023
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '-', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24024
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Responsibilities', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24025
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' include', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24026
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' generating', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24027
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' polished', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24028
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ',', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24029
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' markdown', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24030
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '-form', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24031
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'atted', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24032
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' reports', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24033
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' and', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24034
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' ensuring', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24035
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' final', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24036
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' answers', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24037
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' are', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24038
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' accessible', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24039
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' and', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24040
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' well', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24041
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '-organ', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24042
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'ized', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24043
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '.\n\n', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24044
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '###', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24045
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Naming', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24046
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Context', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24047
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '\n', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24048
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '-', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24049
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' The', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24050
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' name', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24051
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' was', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24052
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' chosen', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24053
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' to', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24054
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' reflect', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24055
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' the', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24056
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' orchestr', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24057
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'ator', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24058
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': "'s", 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24059
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' purpose', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24060
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ':', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24061
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' mastering', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24062
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' the', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24063
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' "', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24064
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' ruh', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24065
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '"', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24066
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' or', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24067
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' core', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24068
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' processes', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24069
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' of', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24070
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' workflow', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24071
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' coordination', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24072
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' and', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24073
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' knowledge', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24074
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' delivery', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24075
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '.\n', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24076
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '-', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24077
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' This', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24078
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' aligns', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24079
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' with', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24080
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' the', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24081
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' broader', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24082
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' aim', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24083
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' of', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24084
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' providing', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24085
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' authoritative', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24086
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ',', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24087
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' streamlined', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24088
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' responses', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24089
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '.\n\n', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24090
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '##', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24091
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Summary', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24092
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '\n', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24093
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'R', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24094
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'uh', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24095
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' Master', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24096
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' is', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24097
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' the', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24098
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' orchestr', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24099
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'ator', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24100
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': "'s", 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24101
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' name', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24102
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ',', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24103
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' representing', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24104
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' a', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24105
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' role', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24106
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' dedicated', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24107
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' to', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24108
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' synthes', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24109
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'izing', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24110
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' and', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24111
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' presenting', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24112
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' information', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24113
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' clearly', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24114
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' and', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24115
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' efficiently', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24116
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '.', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24117
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' The', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24118
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' focus', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24119
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' is', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24120
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' on', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24121
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' delivering', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24122
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' high', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24123
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '-quality', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24124
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ',', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24125
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' organized', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24126
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' answers', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24127
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' to', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24128
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' user', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24129
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': ' queries', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24130
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '.\n\n', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24131
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'TER', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24132
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'MIN', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24133
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': 'ATE', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'streaming_chunk', 'metadata': {'streaming': True, 'chunk_type': 'model_client_streaming'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24134
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': '## Direct Answer\nMy name is Ruh Master. I am a specialized agent focused on strategic workflow orchestration, including synthesizing and summarizing information to deliver comprehensive, user-friendly reports.\n\n## Key Findings/Main Points\n- **Ruh Master** is the designated name for this orchestrator and summarization role.\n- The primary function is to manage, coordinate, and synthesize responses in a clear, structured manner.\n- All outputs are formatted for professionalism and clarity, addressing user needs directly.\n\n## Detailed Analysis\n\n### Role and Function\n- **Ruh Master** acts as a central point for integrating and summarizing information from various agents or sources.\n- Responsibilities include generating polished, markdown-formatted reports and ensuring final answers are accessible and well-organized.\n\n### Naming Context\n- The name was chosen to reflect the orchestrator\'s purpose: mastering the " ruh" or core processes of workflow coordination and knowledge delivery.\n- This aligns with the broader aim of providing authoritative, streamlined responses.\n\n## Summary\nRuh Master is the orchestrator\'s name, representing a role dedicated to synthesizing and presenting information clearly and efficiently. The focus is on delivering high-quality, organized answers to user queries.', 'source': 'SummaryEmployee', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'text', 'metadata': {'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24135
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_1b751550-696a-4cae-ba83-29e6166c8fa4_0e536411-cf35-4962-b544-290684f996d0', 'session_id': '1b751550-696a-4cae-ba83-29e6166c8fa4', 'event_type': 'message_streaming', 'agent_response': {'content': "Task completed: 4 messages, stopped due to Text 'TERMINATE' mentioned", 'source': 'system', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'task_result', 'metadata': {'message_count': 4, 'stop_reason': "Text 'TERMINATE' mentioned", 'usage': None, 'task_completed': True}}, 'success': True, 'message': 'Orchestration team response', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:23 - AgentExecutor - DEBUG - Result consumer received message: Offset=24136
2025-06-28 18:31:23 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'orch_chat_563dff3d-bfd7-4ea7-9619-c4ae62062b45_2db49a84-9f9f-4005-93e8-4501199c21db', 'session_id': '563dff3d-bfd7-4ea7-9619-c4ae62062b45', 'event_type': 'message_streaming', 'agent_response': {}, 'success': True, 'message': 'No human input received, continuing...', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-06-28 18:31:51 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:31:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:31:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:31:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 18:32:51 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:32:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:32:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:32:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 18:33:51 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:33:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:33:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:33:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 18:34:51 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:34:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:34:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:34:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 18:35:25 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1006
2025-06-28 18:35:25 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': **********, 'task_type': 'workflow', 'data': {'workflow_id': 'feda07bf-a91e-4004-80cb-72416cdb5a43', 'payload': {'user_dependent_fields': ['iteration_list'], 'user_payload_template': {'iteration_list': {'value': '["blockchain","automation","healthcare"]', 'transition_id': 'LoopNode-1750863512419'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-06-28 18:35:25 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 18:35:25 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/feda07bf-a91e-4004-80cb-72416cdb5a43
2025-06-28 18:35:26 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-28 18:35:26 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow LOOP TEST DO NOT DELETE retrieved successfully",
  "workflow": {
    "id": "feda07bf-a91e-4004-80cb-72416cdb5a43",
    "name": "LOOP TEST DO NOT DELETE",
    "description": "LOOP_TEST_DO_NOT_DELETE",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/dfbb35aa-5dac-4505-b8ab-e29a2853971a.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/e1da74c2-0744-4dc2-b2c1-d46aca8a8f34.json",
    "start_nodes": [
      {
        "field": "iteration_list",
        "type": "list",
        "transition_id": "transition-LoopNode-1750863512419"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-24T11:13:46.129953",
    "updated_at": "2025-06-28T13:04:23.189580",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-1750769520925"
      },
      {
        "name": "LoopNode",
        "display_name": "For Each Loop",
        "type": "component",
        "transition_id": "transition-LoopNode-1750863512419"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1751052569332"
      },
      {
        "name": "MCP_script-generation-mcp_script_generate",
        "id": "748a8221-d7d9-4352-93ae-00700f4d28b1",
        "transition_id": "transition-MCP_script-generation-mcp_script_generate-1751115762780",
        "type": "mcp",
        "display_name": "script-generation-mcp - script_generate",
        "data": {
          "input_schema": {
            "$defs": {
              "Keywords": {
                "properties": {
                  "time": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Time"
                  },
                  "objective": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Objective"
                  },
                  "audience": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Audience"
                  },
                  "gender": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Gender"
                  },
                  "tone": {
                    "anyOf": [
                      {
                        "type": "string"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Tone"
                  },
                  "speakers": {
                    "anyOf": [
                      {
                        "items": {
                          "type": "string"
                        },
                        "type": "array"
                      },
                      {
                        "type": "null"
                      }
                    ],
                    "default": null,
                    "title": "Speakers"
                  }
                },
                "title": "Keywords",
                "type": "object"
              },
              "ScriptType": {
                "enum": [
                  "VIDEO",
                  "TOPIC",
                  "SCRIPT",
                  "BLOG",
                  "AI"
                ],
                "title": "ScriptType",
                "type": "string"
              },
              "VideoType": {
                "enum": [
                  "SHORT",
                  "LONG"
                ],
                "title": "VideoType",
                "type": "string"
              }
            },
            "properties": {
              "topic": {
                "title": "Topic",
                "type": "string"
              },
              "script_type": {
                "$ref": "#/$defs/ScriptType",
                "default": "TOPIC"
              },
              "keywords": {
                "$ref": "#/$defs/Keywords"
              },
              "video_type": {
                "$ref": "#/$defs/VideoType",
                "default": "SHORT"
              },
              "link": {
                "anyOf": [
                  {
                    "format": "uri",
                    "maxLength": 2083,
                    "minLength": 1,
                    "type": "string"
                  },
                  {
                    "type": "null"
                  }
                ],
                "default": null,
                "title": "Link"
              }
            },
            "required": [
              "topic"
            ],
            "title": "GenerateScriptInput",
            "type": "object"
          },
          "output_schema": {
            "properties": {
              "title": {
                "type": "string",
                "description": "Title of the generated script",
                "title": "title"
              },
              "script": {
                "type": "string",
                "description": "The generated script",
                "title": "script"
              },
              "script_type": {
                "type": "string",
                "description": "Type of the script",
                "title": "script_type"
              },
              "video_type": {
                "type": "string",
                "description": "The type of video",
                "title": "video_type"
              },
              "link": {
                "type": "string",
                "format": "uri",
                "description": "Optional link for the script",
                "title": "link"
              }
            }
          }
        }
      }
    ],
    "is_updated": true
  }
}
2025-06-28 18:35:27 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for feda07bf-a91e-4004-80cb-72416cdb5a43 - server_script_path is optional
2025-06-28 18:35:27 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-28 18:35:27 - StateManager - DEBUG - Using global database connections from initializer
2025-06-28 18:35:27 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 18:35:27 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 18:35:27 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 18:35:27 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 18:35:27 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 18:35:28 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-28 18:35:28 - StateManager - DEBUG - Using provided database connections
2025-06-28 18:35:28 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-28 18:35:28 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-28 18:35:28 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-28 18:35:28 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-28 18:35:28 - StateManager - INFO - WorkflowStateManager initialized
2025-06-28 18:35:28 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751052569332: ['transition-LoopNode-1750863512419']
2025-06-28 18:35:28 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750769520925: ['transition-LoopNode-1750863512419']
2025-06-28 18:35:28 - StateManager - DEBUG - Extracted dependencies for transition transition-MCP_script-generation-mcp_script_generate-1751115762780: ['transition-CombineTextComponent-1750769520925']
2025-06-28 18:35:28 - StateManager - INFO - Built dependency map for 4 transitions
2025-06-28 18:35:28 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751052569332 depends on: ['transition-LoopNode-1750863512419']
2025-06-28 18:35:28 - StateManager - DEBUG - Transition transition-CombineTextComponent-1750769520925 depends on: ['transition-LoopNode-1750863512419']
2025-06-28 18:35:28 - StateManager - DEBUG - Transition transition-MCP_script-generation-mcp_script_generate-1751115762780 depends on: ['transition-CombineTextComponent-1750769520925']
2025-06-28 18:35:28 - MCPToolExecutor - DEBUG - Set correlation ID to: 2b20113c-e3d3-48b5-b7af-2f439e2cb911
2025-06-28 18:35:28 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 2b20113c-e3d3-48b5-b7af-2f439e2cb911 in tool_executor
2025-06-28 18:35:28 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 18:35:28 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-06-28 18:35:28 - NodeExecutor - DEBUG - Set correlation ID to: 2b20113c-e3d3-48b5-b7af-2f439e2cb911
2025-06-28 18:35:28 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 2b20113c-e3d3-48b5-b7af-2f439e2cb911 in node_executor
2025-06-28 18:35:28 - AgentExecutor - DEBUG - Set correlation ID to: 2b20113c-e3d3-48b5-b7af-2f439e2cb911
2025-06-28 18:35:28 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 2b20113c-e3d3-48b5-b7af-2f439e2cb911 in agent_executor
2025-06-28 18:35:28 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-06-28 18:35:28 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-06-28 18:35:28 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-28 18:35:28 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 2b20113c-e3d3-48b5-b7af-2f439e2cb911
2025-06-28 18:35:28 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 2b20113c-e3d3-48b5-b7af-2f439e2cb911
2025-06-28 18:35:28 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 2b20113c-e3d3-48b5-b7af-2f439e2cb911, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-28 18:35:28 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-1750863512419
2025-06-28 18:35:28 - StateManager - DEBUG - State: pending={'transition-LoopNode-1750863512419'}, waiting=set(), completed=set()
2025-06-28 18:35:28 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-1750863512419
2025-06-28 18:35:28 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-1750863512419'}
2025-06-28 18:35:29 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:2b20113c-e3d3-48b5-b7af-2f439e2cb911'
2025-06-28 18:35:29 - RedisManager - DEBUG - Set key 'workflow_state:2b20113c-e3d3-48b5-b7af-2f439e2cb911' with TTL of 600 seconds
2025-06-28 18:35:29 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 2b20113c-e3d3-48b5-b7af-2f439e2cb911. Will be archived to PostgreSQL when Redis key expires.
2025-06-28 18:35:29 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-28 18:35:29 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-28 18:35:29 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-1750863512419'}
2025-06-28 18:35:29 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-28 18:35:29 - StateManager - INFO - Terminated: False
2025-06-28 18:35:29 - StateManager - INFO - Pending transitions (0): []
2025-06-28 18:35:29 - StateManager - INFO - Waiting transitions (0): []
2025-06-28 18:35:29 - StateManager - INFO - Completed transitions (0): []
2025-06-28 18:35:29 - StateManager - INFO - Results stored for 0 transitions
2025-06-28 18:35:29 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 18:35:29 - StateManager - INFO - Workflow status: inactive
2025-06-28 18:35:29 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-28 18:35:29 - StateManager - INFO - Workflow status: inactive
2025-06-28 18:35:29 - StateManager - INFO - Workflow paused: False
2025-06-28 18:35:29 - StateManager - INFO - ==============================
2025-06-28 18:35:29 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-1750863512419
2025-06-28 18:35:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 2b20113c-e3d3-48b5-b7af-2f439e2cb911):
2025-06-28 18:35:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 2b20113c-e3d3-48b5-b7af-2f439e2cb911, response: {'result': 'Starting execution of transition: transition-LoopNode-1750863512419', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-1750863512419', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-28 18:35:29 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-1750863512419' (type=initial, execution_type=loop)
2025-06-28 18:35:29 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-28 18:35:29 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-28 18:35:29 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-1750863512419
2025-06-28 18:35:29 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-28 18:35:29 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-28 18:35:29 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-28 18:35:29 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 18:35:29 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-1750863512419' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["blockchain","automation","healthcare"]', 'batch_size': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-28 18:35:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 2b20113c-e3d3-48b5-b7af-2f439e2cb911):
2025-06-28 18:35:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 2b20113c-e3d3-48b5-b7af-2f439e2cb911, response: {'transition_id': 'transition-LoopNode-1750863512419', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-28 18:35:29 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-1750863512419
2025-06-28 18:35:29 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-1750863512419
2025-06-28 18:35:29 - TransitionHandler - DEBUG - 🔧 Updated loop config iteration_list with resolved data: ["blockchain","automation","healthcare"]
2025-06-28 18:35:29 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-1750769520925 (has current_item/iteration indicators)
2025-06-28 18:35:29 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751052569332 (has final/aggregated indicators)
2025-06-28 18:35:29 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-1750769520925']
2025-06-28 18:35:30 - TransitionHandler - ERROR - Tool execution failed for tool 'LoopNode' (tool_id: 1) in node 'LoopNode' of transition 'transition-LoopNode-1750863512419': iteration_list must be an arrayTraceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 426, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 129, in execute_tool
    parsed_config = self.parse_loop_config(loop_config)
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 377, in parse_loop_config
    self._validate_new_schema_raw(loop_config)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1156, in _validate_new_schema_raw
    raise ValueError("iteration_list must be an array")
ValueError: iteration_list must be an array

2025-06-28 18:35:30 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 2b20113c-e3d3-48b5-b7af-2f439e2cb911):
2025-06-28 18:35:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 2b20113c-e3d3-48b5-b7af-2f439e2cb911, response: {'transition_id': 'transition-LoopNode-1750863512419', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: iteration_list must be an array', 'status': 'failed', 'sequence': 2, 'workflow_status': 'running'}
2025-06-28 18:35:30 - TransitionHandler - ERROR - Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array
2025-06-28 18:35:30 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array')]
2025-06-28 18:35:30 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-LoopNode-1750863512419: Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array
2025-06-28 18:35:30 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-LoopNode-1750863512419: NoneType: None

2025-06-28 18:35:30 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array
2025-06-28 18:35:30 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 426, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 129, in execute_tool
    parsed_config = self.parse_loop_config(loop_config)
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 377, in parse_loop_config
    self._validate_new_schema_raw(loop_config)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1156, in _validate_new_schema_raw
    raise ValueError("iteration_list must be an array")
ValueError: iteration_list must be an array

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 684, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 263, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 227, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array

2025-06-28 18:35:30 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array
2025-06-28 18:35:30 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 426, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 129, in execute_tool
    parsed_config = self.parse_loop_config(loop_config)
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 377, in parse_loop_config
    self._validate_new_schema_raw(loop_config)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1156, in _validate_new_schema_raw
    raise ValueError("iteration_list must be an array")
ValueError: iteration_list must be an array

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 684, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 280, in execute
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 263, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 227, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array

2025-06-28 18:35:30 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array
2025-06-28 18:35:30 - KafkaWorkflowConsumer - INFO - Workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43' final status: failed, result: Exception in workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43': Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array
2025-06-28 18:35:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 2b20113c-e3d3-48b5-b7af-2f439e2cb911, response: {'status': 'failed', 'result': "Exception in workflow 'feda07bf-a91e-4004-80cb-72416cdb5a43': Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array", 'workflow_status': 'failed', 'error': 'Exception in transition transition-LoopNode-1750863512419: Tool execution error: [ERROR] Tool Execution Failed with error: iteration_list must be an array', 'error_type': 'Exception'}
2025-06-28 18:35:30 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 2b20113c-e3d3-48b5-b7af-2f439e2cb911 
2025-06-28 18:35:30 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1006, corr_id: 2b20113c-e3d3-48b5-b7af-2f439e2cb911
2025-06-28 18:35:51 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:35:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:35:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:35:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 18:36:51 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:36:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:36:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:36:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 18:37:51 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:37:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:37:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:37:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 18:38:51 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:38:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:38:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:38:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-28 18:39:51 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-28 18:39:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:39:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-28 18:39:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
